# -*- coding: utf-8 -*-
import time
import json
import redis
import base64
import snappy
import requests
from queue import Queue
from loguru import logger
from threading import Thread
from utils.config import redis_host, redis_port, redis_password, msg_kafka_key, group_kafka_key, sender_kafka_key, user_kafka_key, log_path, error_key


class submitData(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.session = requests.Session()

        self.blocking = Queue()
        pool = redis.ConnectionPool(host=redis_host, port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True)
        self.redis_client = redis.Redis(connection_pool=pool)

        self.count_number = 500

    def send_err_mess(self):
        """
        发送报警信息
        :return:
        """
        while True:
            desc = self.redis_client.spop(error_key)
            if desc:
                try:
                    json_data = {'code': 7, 'desc': desc}
                    resp = self.session.post('https://alert.andata.cn/v1/alarm', json=json_data)
                    if resp.json().get("code") != 200:
                        self.redis_client.sadd(error_key, desc)
                except Exception as e:
                    logger.error(e)
                    self.redis_client.sadd(error_key, desc)
            else:
                time.sleep(20)

    def send_msg_kafka(self):
        """
        提交数据到kafka
        :return:
        """
        while True:
            cache = self.redis_client.scard(msg_kafka_key)
            if cache > self.count_number:
                data = self.redis_client.spop(name=msg_kafka_key, count=self.count_number)
                if data:
                    try:
                        messages = [json.loads(_) for _ in data]
                        self.http2kafka(self.snappy_encrypt(messages))
                    except Exception as e:
                        logger.exception(e)
                        error_mess = f"tg推送数据失败"
                        self.redis_client.sadd(error_key, error_mess)
                        self.redis_client.sadd(msg_kafka_key, *data)
            else:
                time.sleep(1)

    def send_group_kafka(self):
        """
        提交数据到kafka
        :return:
        """
        while True:
            cache = self.redis_client.scard(group_kafka_key)
            if cache > 0:
                data = self.redis_client.spop(name=group_kafka_key, count=self.count_number)
                if data:
                    try:
                        messages = [json.loads(_) for _ in data]
                        self.http2kafka(self.snappy_encrypt(messages))
                    except Exception as e:
                        logger.exception(e)
                        error_mess = f"tg推送数据失败"
                        self.redis_client.sadd(error_key, error_mess)
                        self.redis_client.sadd(group_kafka_key, *data)
            time.sleep(120)

    def send_user_kafka(self):
        """
        提交数据到kafka
        :return:
        """
        while True:
            cache = self.redis_client.scard(user_kafka_key)
            if cache > 0:
                data = self.redis_client.spop(name=user_kafka_key, count=self.count_number)
                if data:
                    try:
                        messages = [json.loads(_) for _ in data]
                        self.http2kafka(self.snappy_encrypt(messages))
                    except Exception as e:
                        logger.exception(e)
                        error_mess = f"tg推送数据失败"
                        self.redis_client.sadd(error_key, error_mess)
                        self.redis_client.sadd(user_kafka_key, *data)
            else:
                time.sleep(1)

    def send_sender_kafka(self):
        """
        提交数据到kafka
        :return:
        """
        while True:
            cache = self.redis_client.scard(sender_kafka_key)
            if cache > self.count_number:
                data = self.redis_client.spop(name=sender_kafka_key, count=self.count_number)
                if data:
                    try:
                        messages = [json.loads(_) for _ in data]
                        self.http2kafka(self.snappy_encrypt(messages))
                    except Exception as e:
                        logger.exception(e)
                        error_mess = f"tg推送数据失败"
                        self.redis_client.sadd(error_key, error_mess)
                        self.redis_client.sadd(sender_kafka_key, *data)
            else:
                time.sleep(1)

    def http2kafka(self, messages, max_freq=3):
        num = 0
        while num < max_freq:
            try:
                resp = self.session.post("http://111.230.217.123:59092/ckafka/andata_tg_spider", data=messages, timeout=10)
                if resp.status_code == 204:
                    logger.info("submit success")
                    return True
                else:
                    logger.warning(resp.json())
                    num += 1
            except (Exception,) as e:
                logger.warning(e)
                time.sleep(0.1)
                num += 1
        logger.warning("submit fall")
        return False

    @staticmethod
    def snappy_encrypt(messages: list):
        body = json.dumps(messages, ensure_ascii=False)
        compress = snappy.compress(body)
        text = base64.b64encode(compress).decode("utf-8")
        return text

    def start(self, thread_num=50):
        self.blocking.put(1)
        thread_list = [Thread(target=self.send_msg_kafka, ) for _ in range(thread_num)]
        thread_list.extend([Thread(target=self.send_sender_kafka, ) for _ in range(thread_num)])
        thread_list.extend([Thread(target=self.send_group_kafka, ) for _ in range(1)])
        thread_list.extend([Thread(target=self.send_user_kafka, ) for _ in range(1)])
        thread_list.extend([Thread(target=self.send_err_mess, ) for _ in range(1)])
        for t in thread_list:
            t.setDaemon(False)
            t.start()

        time.sleep(1)
        for q in [self.blocking]:
            q.join()


if __name__ == '__main__':
    submit_data = submitData()
    submit_data.start(40)
