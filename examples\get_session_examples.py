#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StringSession获取示例
演示如何使用不同方式获取Telegram StringSession
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from get_StringSession import StringSessionGenerator, quick_generate_session

async def example_1_simple_phone_login():
    """示例1: 简单的手机号登录"""
    print("=== 示例1: 手机号登录 ===")
    
    generator = StringSessionGenerator()
    
    # 替换为你的手机号
    phone = "+*************"  # 示例手机号
    
    result = await generator.get_session_by_phone(phone)
    
    if result:
        print("✅ 成功获取StringSession:")
        print(f"手机号: {result['phone']}")
        print(f"StringSession: {result['account_key']}")
        
        # 保存到文件
        generator.save_sessions_to_file([result], "example_account.json")
    else:
        print("❌ 获取失败")

async def example_2_qr_login():
    """示例2: 二维码登录"""
    print("=== 示例2: 二维码登录 ===")
    
    generator = StringSessionGenerator()
    result = await generator.get_session_by_qr()
    
    if result:
        print("✅ 二维码登录成功:")
        print(f"StringSession: {result['account_key']}")

async def example_3_batch_generation():
    """示例3: 批量生成"""
    print("=== 示例3: 批量生成 ===")
    
    # 示例手机号列表（请替换为真实手机号）
    phone_numbers = [
        "+*************",
        "+*************", 
        "+*************"
    ]
    
    generator = StringSessionGenerator()
    results = await generator.batch_generate_sessions(phone_numbers)
    
    if results:
        print(f"✅ 成功生成 {len(results)} 个StringSession")
        generator.save_sessions_to_file(results, "batch_accounts.json")

async def example_4_with_proxy():
    """示例4: 使用代理"""
    print("=== 示例4: 使用代理 ===")
    
    # 代理配置示例
    proxy = {
        'proxy_type': 'socks5',
        'addr': '127.0.0.1',
        'port': 1080,
        'username': None,  # 如果需要认证
        'password': None   # 如果需要认证
    }
    
    generator = StringSessionGenerator()
    phone = "+*************"
    
    result = await generator.get_session_by_phone(phone, proxy)
    
    if result:
        print("✅ 通过代理获取成功")

async def example_5_quick_function():
    """示例5: 使用快速函数"""
    print("=== 示例5: 快速函数 ===")
    
    phone = "+*************"
    session_string = await quick_generate_session(phone)
    
    if session_string:
        print(f"✅ StringSession: {session_string}")

async def example_6_validate_session():
    """示例6: 验证StringSession"""
    print("=== 示例6: 验证StringSession ===")
    
    # 示例StringSession（请替换为真实的）
    session_string = "你的StringSession字符串"
    
    generator = StringSessionGenerator()
    
    from telethon import TelegramClient
    from telethon.sessions import StringSession
    
    client = TelegramClient(StringSession(session_string), generator.api_id, generator.api_hash)
    
    try:
        await client.connect()
        
        if await client.is_user_authorized():
            me = await client.get_me()
            print("✅ StringSession有效")
            print(f"用户: {me.first_name}")
        else:
            print("❌ StringSession无效")
    
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    
    finally:
        await client.disconnect()

async def main():
    """运行所有示例"""
    print("🔐 StringSession获取示例")
    print("注意: 请将示例中的手机号替换为真实手机号")
    print("="*60)
    
    # 选择要运行的示例
    examples = [
        ("手机号登录", example_1_simple_phone_login),
        ("二维码登录", example_2_qr_login),
        ("批量生成", example_3_batch_generation),
        ("使用代理", example_4_with_proxy),
        ("快速函数", example_5_quick_function),
        ("验证Session", example_6_validate_session)
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    
    choice = input("\n请选择要运行的示例 (1-6, 或按回车运行第一个): ").strip()
    
    if not choice:
        choice = "1"
    
    try:
        index = int(choice) - 1
        if 0 <= index < len(examples):
            name, func = examples[index]
            print(f"\n运行示例: {name}")
            await func()
        else:
            print("❌ 无效选择")
    except ValueError:
        print("❌ 请输入数字")

if __name__ == "__main__":
    asyncio.run(main())
