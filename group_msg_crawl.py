# -*- coding: utf-8 -*-
# @Time : 2023/8/22 14:37
# @Site : 
# @File : group_msg_crawl.py
# @Software: PyCharm
import os
import re
import sys
import json
import random
import asyncio
import aioredis
from loguru import logger
from telethon.types import User
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import UsernameInvalidError

sys.path.append(os.path.join(os.getcwd()))
from utils.crawl_tools import get_history_msg, get_machine_ip, get_group_info, is_russian
from utils.config import redis_host, redis_port, redis_password, api_id, api_hash, log_path, error_key, failure_key


class GroupMsgCrawl(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # redis客户端
        self.redis_client = aioredis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)
        # 补充采集链接的key
        self.missing_data_key = "tg_spider:crawl:missing_data_info"
        # 补充采集成功的key
        self.missing_data_success_key = "tg_spider:crawl:missing_data_success:{}"
        # 历史消息采集key
        self.history_task_key = "tg_spider:crawl:history:task_links"
        # 历史采集成功的key
        self.history_success_key = "tg_spider:crawl:history:crawl_success"
        # 频道链接key
        self.broadcast_links_key = "tg_spider:crawl:history:broadcast_links"
        # 每日更新采集链接的key
        self.updated_task_key = "tg_spider:crawl:history:crawl_success"
        # 更新采集成功的key
        self.updated_success_key = "tg_spider:crawl:update_success:{}"
        # 每日订阅采集链接的key
        self.subscription_task_key = "tg_spider:crawl:subscription_links"
        self.subscription_success_key = "tg_spider:crawl:subscription_success:{}"
        self.subscription_failed_key = "tg_spider:crawl:subscription_failed"
        # 账号的key
        self.account_key = "tg_spider:account:{}"
        # 登录失败的key
        self.account_banned_key = "tg_spider:account_banned"
        # 采集次数的key
        self.crawl_count_key = "tg_spider:crawl:crawl_count:{}"
        # 群组采集的最大记录
        self.offset_key = "tg_spider:crawl:max_offset"
        # 群组信息的key
        self.group_info_key = "tg_spider:group_info"
        # 单个账号最大的采集次数
        self.max_requests_number = 0
        # 消息获取数量
        self.msg_num = 0

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群消息采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(self.account_banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            if "The authorization key (session file) was used under two different IP addresses simultaneously" in str(e):
                await self.redis_client.sadd(self.account_banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
            error_mess = f"群消息采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            await self.redis_client.sadd(error_key, error_mess)

    async def subscription_crawl(self, account_info, machine_ip):
        """
        订阅群更新
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        phone = account_info.get("phone")
        client = await self.login(account_info, machine_ip)
        assert client
        crawl_count_key = self.crawl_count_key.format(phone)
        crawl_count = await self.redis_client.get(crawl_count_key) or 0
        async with client:
            while int(crawl_count) < self.max_requests_number:
                try:
                    msg_total = 0
                    # 开始采集
                    link = await self.redis_client.spop(self.subscription_task_key)
                    if not link:
                        break
                    subscription_success = self.subscription_success_key.format(link)
                    success = await self.redis_client.get(subscription_success)
                    is_failed = await self.redis_client.sismember(self.subscription_failed_key, link)
                    if success or is_failed:
                        continue
                    # 获取最新的还是最旧的消息,True是以offset为起点进行最新的抓取
                    reverse = True
                    entity = await client.get_entity(link)
                    if isinstance(entity, User):
                        await self.redis_client.sadd(self.subscription_failed_key, link)
                        await self.redis_client.srem(self.subscription_task_key, link)
                        continue
                    # 获取记录到的最大消息id
                    offset = await self.redis_client.hget(self.offset_key, entity.id) or 0
                    logger.info(f"开始更新订阅群信息:【{link}】,消息偏移量:【{offset}】")
                    await get_group_info(client, link, entity)
                    while True:
                        try:
                            # 获取消息
                            msgs = await get_history_msg(client, self.msg_num, link, int(offset), reverse, entity)
                            msg_len = len(msgs)
                            if crawl_count == 0:
                                await self.redis_client.set(crawl_count_key, value="1", ex=24 * 60 * 60)
                            else:
                                ttl = await self.redis_client.ttl(crawl_count_key)
                                if ttl == -1:
                                    await self.redis_client.expire(crawl_count_key, 24 * 60 * 60)
                                if msg_len > 0:
                                    await self.redis_client.incr(crawl_count_key)
                            if msg_len > 0:
                                offset = msgs[-1].get("msgId", 0)
                                logger.info(f"最新的偏移量为{offset}")
                                await self.redis_client.hset(self.offset_key, entity.id, offset)
                                msg_total += msg_len
                                if msg_len < self.msg_num - 10:
                                    break
                            else:
                                await asyncio.sleep(5)
                                break
                            crawl_count = await self.redis_client.get(crawl_count_key)
                            if int(crawl_count) >= self.max_requests_number:
                                break
                        except (Exception,) as e:
                            logger.exception(e)
                    if msg_total > 0:
                        await self.redis_client.set(subscription_success, link, ex=60 * 60 * 24 * 1)
                    else:
                        await self.redis_client.set(subscription_success, link, ex=60 * 60 * 24 * 2)
                    logger.info(f"【{link}】更新订阅群信息完毕 共采集了【{msg_total}】条消息")
                except (UsernameInvalidError,):
                    logger.error(f"【{link}】更新订阅群信息错误,群不存在")
                    await self.redis_client.sadd(failure_key, link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot cast InputPeerUser " in str(e) or "Cannot find any entity corresponding" in str(e):
                        logger.error(f"【{link}】更新订阅群信息错误,群不存在")
                        await self.redis_client.sadd(self.subscription_failed_key, link)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{link}】用户尝试加入的聊天已过期，不再有效")
                        await self.redis_client.sadd(self.subscription_failed_key, link)
                    elif "Join the group and retry" in str(e) or "The channel specified is private and you lack permission to access it":
                        await self.redis_client.hset("tg_spider:politics_links", link, link)
                        await self.redis_client.srem(self.subscription_task_key, link)
                    elif "Cannot send requests while disconnected" in str(e):
                        break
                    elif "Cannot get entity from a channel" in str(e):
                        await self.redis_client.sadd(self.subscription_failed_key, link)
                    else:
                        await self.redis_client.sadd(self.subscription_task_key, link)
                finally:
                    crawl_count = await self.redis_client.get(crawl_count_key) or 0
                    await asyncio.sleep(5)
        await client.disconnect()
        logger.info(f"【{phone}】今日请求次数已经消耗完,共请求【{crawl_count}】次")

    async def missing_data_crawl(self, account_info, machine_ip):
        """
        消息补充采集(漏数据)
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        phone = account_info.get("phone")
        client = await self.login(account_info, machine_ip)
        assert client
        crawl_count_key = self.crawl_count_key.format(phone)
        crawl_count = await self.redis_client.get(crawl_count_key) or 0
        # 开始连接账号
        async with client:
            while int(crawl_count) < self.max_requests_number:
                try:
                    msg_total = 0
                    # 开始采集
                    crawl_task = await self.redis_client.spop(self.missing_data_key)
                    crawl_json = json.loads(crawl_task)
                    link = crawl_json.get('link')
                    offset = crawl_json.get('min_id')
                    max_offset = int(crawl_json.get('max_id'))
                    logger.info(f"开始群消息补采:【{link}】")
                    ismember = await self.redis_client.get(self.missing_data_success_key.format(link)) or await self.redis_client.sismember(failure_key, link)
                    if ismember:
                        continue
                    # 获取最新的还是最旧的消息,这里改成True
                    reverse = True
                    entity = await client.get_entity(link)
                    channel_name = entity.title
                    if "呻吟群" in channel_name or "ASMR" in channel_name.upper() or "赏片堂-中文字幕" in channel_name or is_russian(channel_name):
                        continue
                    while True:
                        try:
                            # 获取历史消息
                            update_msg = await get_history_msg(client, self.msg_num, link, int(offset), reverse, entity)
                            if crawl_count == 0:
                                await self.redis_client.set(crawl_count_key, value="1", ex=24 * 60 * 60)
                            else:
                                await self.redis_client.incr(crawl_count_key)
                            update_msg_len = len(update_msg)
                            if update_msg_len > 0:
                                offset = update_msg[-1].get("msgId", 0)
                                msg_total += update_msg_len
                                if max_offset != 0 and offset >= max_offset:
                                    break
                                if max_offset == 0 and update_msg_len < 900:
                                    break
                            else:
                                await asyncio.sleep(5)
                                break
                            crawl_count = await self.redis_client.get(crawl_count_key)
                            if int(crawl_count) >= self.max_requests_number:
                                if offset < max_offset:
                                    self.redis_client.sadd(self.missing_data_key, json.dumps({"link": link, "min_id": offset, "max_id": max_offset}))
                                break
                        except (Exception,) as e:
                            logger.exception(e)
                    logger.info(f"【{link}】群消息补采完毕 共采集了【{msg_total}】条消息")
                except(UsernameInvalidError,):
                    logger.error(f"【{link}】群消息补采错误,群不存在")
                    await self.redis_client.sadd(failure_key, link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot cast InputPeerUser " in str(e):
                        logger.error(f"【{link}】更新群消息错误,群不存在")
                        await self.redis_client.sadd(failure_key, link)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{link}】用户尝试加入的聊天已过期，不再有效")
                        await self.redis_client.sadd(failure_key, link)
                    elif "Join the group and retry" in str(e):
                        await self.redis_client.hset("tg_spider:politics_links", link, link)
                    elif "Cannot send requests while disconnected" in str(e):
                        await self.redis_client.sadd(self.history_task_key, link)
                        break
                    else:
                        await self.redis_client.sadd(self.missing_data_key, crawl_task)
                finally:
                    crawl_count = await self.redis_client.get(crawl_count_key) or 0
        await client.disconnect()
        logger.info(f"【{phone}】今日请求次数已经消耗完,共请求【{crawl_count}】次")

    async def history_msg_crawl(self, account_info, machine_ip):
        """
        历史消息采集
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        phone = account_info.get("phone")
        client = await self.login(account_info, machine_ip)
        assert client
        crawl_count_key = self.crawl_count_key.format(phone)
        crawl_count = await self.redis_client.get(crawl_count_key) or 0
        # 开始连接账号
        async with client:
            while int(crawl_count) < self.max_requests_number:
                try:
                    msg_total = 0
                    # 开始采集
                    link = await self.redis_client.spop(self.history_task_key)
                    if not link or link == "https://t.me/shangpian":
                        continue
                    is_pass = await self.redis_client.sismember(self.history_success_key, link) or await self.redis_client.sismember(failure_key, link)
                    if is_pass:
                        continue
                    # 采集配置
                    reverse = False
                    entity = await client.get_entity(link)
                    if isinstance(entity, User):
                        continue
                    channel_name = entity.title
                    if "呻吟群" in channel_name or "ASMR" in channel_name.upper() or "赏片堂-中文字幕" in channel_name or is_russian(channel_name) or "asmr" in channel_name:
                        continue
                    logger.info(f"开始采集历史信息:【{channel_name}】,link: 【{link}】")
                    switch_to_update_mode = False
                    offset = await self.redis_client.hget(self.offset_key, entity.id) or 0  # 获取记录到的最大消息id
                    # 采集群组基础信息
                    await get_group_info(client, link, entity)
                    await self.redis_client.hset(self.group_info_key, entity.id, link)
                    times = 0
                    if offset != 0:
                        logger.info(f"【{link}】之前已经采集过")
                        switch_to_update_mode = True
                        reverse = True
                    # 获取历史消息
                    while True:
                        try:
                            await asyncio.sleep(1)
                            msgs = await get_history_msg(client, self.msg_num, link, int(offset), reverse, entity)
                            msg_len = len(msgs)
                            if crawl_count == 0:
                                await self.redis_client.set(crawl_count_key, value="1", ex=24 * 60 * 60)
                            else:
                                if msg_len > 0:
                                    await self.redis_client.incr(crawl_count_key)
                            if msg_len > 0:
                                offset = msgs[-1].get("msgId", 0)
                                if switch_to_update_mode:
                                    logger.info(f"最新的偏移量为{offset}")
                                    await self.redis_client.hset(self.offset_key, entity.id, offset)
                                elif times == 0:
                                    max_offset = msgs[0].get("msgId", 0)
                                    await self.redis_client.hset(self.offset_key, entity.id, max_offset)
                                msg_total += msg_len
                            else:
                                break
                            if offset < 2 or msg_total >= 10000:
                                break
                            times += 1
                            if msg_total > 0:
                                await self.redis_client.sadd(self.history_success_key, link)
                        except (Exception,) as e:
                            logger.exception(e)
                    logger.info(f"【{link}】历史信息采集完毕 共采集了【{msg_total}】条消息")
                except(UsernameInvalidError,):
                    logger.error(f"【{link}】获取历史消息错误,群不存在")
                    await self.redis_client.sadd(failure_key, link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot cast InputPeerUser " in str(e):
                        logger.error(f"【{link}】群信息错误,群不存在")
                        await self.redis_client.sadd(failure_key, link)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{link}】用户尝试加入的聊天已过期，不再有效")
                        await self.redis_client.sadd(failure_key, link)
                    elif "Join the group and retry" in str(e):
                        await self.redis_client.hset("tg_spider:politics_links", link, link)
                    elif "Cannot send requests while disconnected" in str(e):
                        await self.redis_client.sadd(self.history_task_key, link)
                        break
                    else:
                        await self.redis_client.sadd(self.history_task_key, link)
                    # 提示太快了,需要休息
                    if "A wait of " in str(e):
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            if wait_time_int > 500:
                                break
                            else:
                                await asyncio.sleep(wait_time_int)
                finally:
                    crawl_count = await self.redis_client.get(crawl_count_key) or 0
                    await asyncio.sleep(5)
        await client.disconnect()
        logger.info(f"【{phone}】今日请求次数已经消耗完,共请求【{crawl_count}】次")

    async def group_msg_update(self, account_info, machine_ip):
        """
        消息更新采集
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        phone = account_info.get("phone")
        # 开始连接账号
        client = await self.login(account_info, machine_ip)
        assert client
        crawl_count_key = self.crawl_count_key.format(phone)
        crawl_count = await self.redis_client.get(crawl_count_key) or 0
        async with client:
            while int(crawl_count) < self.max_requests_number:
                try:
                    msg_total = 0
                    # 开始采集
                    link = await self.redis_client.srandmember(self.updated_task_key)
                    is_pass = await self.redis_client.get(self.updated_success_key.format(link)) or await self.redis_client.sismember(failure_key, link) or await self.redis_client.sismember(self.broadcast_links_key, link)
                    if is_pass:
                        continue
                    # 获取最新的还是最旧的消息,这里改成True
                    reverse = True
                    entity = await client.get_entity(link)
                    if isinstance(entity, User):
                        await self.redis_client.srem(self.updated_task_key, link)
                        continue
                    broadcast = entity.broadcast
                    if broadcast:
                        await self.redis_client.sadd(self.broadcast_links_key, link)
                        continue
                    channel_name = entity.title
                    if "呻吟群" in channel_name or "ASMR" in channel_name.upper() or "赏片堂-中文字幕" in channel_name or is_russian(channel_name):
                        continue
                    offset = await self.redis_client.hget(self.offset_key, entity.id) or 0  # 获取记录到的最大消息id
                    logger.info(f"开始更新群信息:【{link}】,消息偏移量:【{offset}】")
                    await get_group_info(client, link, entity)
                    while True:
                        try:
                            # 获取历史消息
                            msgs = await get_history_msg(client, self.msg_num, link, int(offset), reverse, entity)
                            msg_len = len(msgs)
                            if crawl_count == 0:
                                await self.redis_client.set(crawl_count_key, value="1", ex=24 * 60 * 60)
                            else:
                                ttl = await self.redis_client.ttl(crawl_count_key)
                                if ttl == -1:
                                    await self.redis_client.expire(crawl_count_key, 24 * 60 * 60)
                                if msg_len > 0:
                                    await self.redis_client.incr(crawl_count_key)
                            if msg_len > 0:
                                offset = msgs[-1].get("msgId", 0)
                                logger.info(f"最新的偏移量为{offset}")
                                await self.redis_client.hset(self.offset_key, entity.id, offset)
                                msg_total += msg_len
                                if msg_len < self.msg_num - 10:
                                    break
                            else:
                                await asyncio.sleep(5)
                                break
                            crawl_count = await self.redis_client.get(crawl_count_key)
                            if int(crawl_count) >= self.max_requests_number:
                                break
                        except (Exception,) as e:
                            logger.exception(e)
                    if msg_total > 0:
                        await self.redis_client.set(self.updated_success_key.format(link), link, ex=60 * 60 * 24 * 3)
                    else:
                        await self.redis_client.set(self.updated_success_key.format(link), link, ex=60 * 60 * 24 * 2)
                    logger.info(f"【{link}】更新群信息完毕 共采集了【{msg_total}】条消息")
                except(UsernameInvalidError,):
                    logger.error(f"【{link}】更新群信息错误,群不存在")
                    await self.redis_client.sadd(failure_key, link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot cast InputPeerUser " in str(e):
                        logger.error(f"【{link}】更新群信息错误,群不存在")
                        await self.redis_client.sadd(failure_key, link)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{link}】用户尝试加入的聊天已过期，不再有效")
                        await self.redis_client.sadd(failure_key, link)
                    elif "Join the group and retry" in str(e):
                        await self.redis_client.hset("tg_spider:politics_links", link, link)
                    elif "Cannot send requests while disconnected" in str(e):
                        await self.redis_client.sadd(self.history_task_key, link)
                        break
                    else:
                        await self.redis_client.sadd(self.updated_task_key, link)
                finally:
                    crawl_count = await self.redis_client.get(crawl_count_key) or 0
                    await asyncio.sleep(5)
        await client.disconnect()
        logger.info(f"【{phone}】今日请求次数已经消耗完,共请求【{crawl_count}】次")

    async def start_crawler(self):
        while True:
            try:
                crawler_seting = await self.redis_client.get("tg_spider:messages_crawler_config")
                messages_crawler_config = json.loads(crawler_seting)
                self.max_requests_number = messages_crawler_config["max_requests_number"]
                self.msg_num = messages_crawler_config["msg_num"]
                task_priority = messages_crawler_config["task_priority"]
                machine_ip = get_machine_ip()
                account_key = self.account_key.format(machine_ip)
                account_list = await self.redis_client.get(account_key) or "[]"
                accounts = json.loads(account_list)
                if len(accounts) == 0:
                    logger.warning(f"【{machine_ip}】下没有绑定的账号")
                    continue
                # 对账号按照请求次数排序,优先使用已经采集的账号进行登录采集
                sort_accounts = []
                for account_info in accounts:
                    phone = account_info.get("phone")
                    crawl_count_key = self.crawl_count_key.format(phone)
                    crawl_count = await self.redis_client.get(crawl_count_key) or 0
                    if int(crawl_count) > 0 and int(crawl_count) >= 100:
                        logger.warning(f"【{phone}】今日请求次数已经消耗完了,共请求了【{crawl_count}】次")
                        continue
                    else:
                        account_info["crawl_count"] = int(crawl_count)
                        sort_accounts.append(account_info)
                random.shuffle(sort_accounts)
                subscription_list = await self.redis_client.smembers("tg_spider:subscription_crawl_ip")
                for task_number, account_info in enumerate(sort_accounts):
                    try:
                        # 优先采集订阅任务
                        subscription_task_number = await self.redis_client.scard(self.subscription_task_key)
                        if machine_ip in list(subscription_list) or subscription_task_number > 20:
                            await self.subscription_crawl(account_info, machine_ip)
                        elif task_priority == "history":
                            await self.history_msg_crawl(account_info, machine_ip)
                        elif task_priority == "update":
                            await self.group_msg_update(account_info, machine_ip)
                        else:
                            if task_number % 3 == 0:
                                await self.history_msg_crawl(account_info, machine_ip)
                            else:
                                await self.group_msg_update(account_info, machine_ip)
                    except (Exception,) as e:
                        logger.exception(e)
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 5)


if __name__ == "__main__":
    group_msg_crawl = GroupMsgCrawl()
    asyncio.run(group_msg_crawl.start_crawler())
