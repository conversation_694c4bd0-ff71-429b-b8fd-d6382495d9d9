# -*- coding: utf-8 -*-
# @Time : 2024/3/7 14:20
# @Site : 
# @File : convert.py
# @Software: PyCharm
import os
import re
import json
import httpx
import random
import asyncio
from telethon import TelegramClient
from telethon.sessions import StringSession

from config import redis_client
from utils.developer_infos import developer_list


async def get_proxy(proxy_key, max_number=10, reselect=False):
    if reselect is False:
        proxy_str = await redis_client.get(proxy_key)
        if proxy_str:
            proxy = json.loads(proxy_str)
            return proxy
    while max_number > 0:
        try:
            await asyncio.sleep(0.5)
            resp = httpx.get("http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1", headers={"Token": "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            proxy = {
                'proxy_type': 'http',
                'addr': "gate1.ipweb.cc",
                'port': 7778,
                'username': username,
                'password': password,
                'rdns': True
            }
            await redis_client.set(proxy_key, json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
            return proxy
        except (Exception,) as e:
            max_number -= 1
            await asyncio.sleep(1)


async def _convert_to_stringsession(sessionfile_path):
    """
    将session文件转换成StringSession
    """
    phone = re.search("\d+(?=_)", sessionfile_path).group()
    proxy_key = "tg_spider:account_temp_proxy:{}".format(phone)
    proxy = await get_proxy(proxy_key)
    while True:
        try:
            developer_info = random.choice(developer_list)
            api_id = developer_info["api_id"]
            api_hash = developer_info["api_hash"]
            client = TelegramClient(sessionfile_path, api_id=api_id, api_hash=api_hash, proxy=proxy, timeout=10)
            await client.connect()
            is_user = await client.is_user_authorized()
            if is_user:
                await redis_client.set(proxy_key.format(phone), json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
                session = StringSession.save(client.session)
                await redis_client.sadd("tg_spider:phone_search:account", json.dumps({"phone": phone, "account_key": session}, ensure_ascii=False))
                os.remove(sessionfile_path)
                await client.disconnect()
                return {"phone": phone, "account_key": session}
            else:
                os.remove(sessionfile_path)
                await redis_client.sadd("tg_spider:loginFail", phone)
                return "被封号"
        except OSError as e:
            print(sessionfile_path, e)
            proxy = await get_proxy(proxy_key, reselect=True)
            continue


async def convert():
    session_path = "/andata/server/crawler/session/"
    f_list = os.listdir(session_path)
    for f in f_list:
        sessionfile_path = session_path + f
        phone = re.search("\d+(?=_)", sessionfile_path).group()
        if await redis_client.sismember("tg_spider:phone_search:account_phone", phone):
            continue
        print(await _convert_to_stringsession(sessionfile_path))


if __name__ == '__main__':
    asyncio.run(convert())
