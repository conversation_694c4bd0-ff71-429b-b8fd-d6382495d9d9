# -*- coding: utf-8 -*-
# @Time : 2024/3/13 15:43
# @Site : 
# @File : get_login_code.py
# @Software: PyCharm
import time
import httpx
import random
import asyncio
from telethon.sessions import StringSession
from telethon import TelegramClient, events
from telethon.tl.functions.account import GetAuthorizationsRequest, ResetAuthorizationRequest
from utils.developer_infos import developer_list

ipweb_url = "http://api.ipweb.cc:8004/api/agent/account2?country=HK&times=90&limit=1"
ipweb_token = "GJ7VJ0URV2AOSAUET5WSN8O7MB4GCKG0"


def get_ipweb_proxy_tg(max_number=10):
    while max_number > 0:
        try:
            resp = httpx.get(ipweb_url, headers={"Token": ipweb_token})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            proxy = {
                'proxy_type': 'http',
                'addr': "gate1.ipweb.cc",
                'port': 7778,
                'username': username,
                'password': password,
                'rdns': True
            }
            return proxy
        except (Exception,) as e:
            max_number -= 1
            time.sleep(1)


account_infos = [
    {"phone": "*************", "account_key": "1BVtsOMIBu70bilV8qGUpAjy2J9_v8LgfX9Bk5LJgmtdnGtPIqNpP_-mA8YMKhhv4sv4fis0gfSwogg81QzSpUVYUyGju7350ISZwsjsH4pkGWqM5vZjaudPv-TxXbVXK4vtoMGkV_RHvUawqy_wOx-0kZeT88x3fai6n1qh2hWlOpku0ddoNdfmXbRGlFe2MjBTiNNBExTmD9rplu95MN68BhIKOk-cjRCF65p5PXRBqggAcjYtN96uva5IebyelhYVgl28tczu573MDiAbBOLUcKhS5xmWwEM6z1q4wdnv3UQp74Tn1NkaS_u8Y4Nli68rwTUnrE00QoRnKlMxNpeC8aPtR5VY=", "email_account": "<EMAIL>", "api_id": "********", "api_hash": "8371f02ea3007074d1704a0c2b4df46d"},
    # {"phone": "*************", "account_key": "1BVtsOHYBu50GrYaMoYc8eSJbH8yBREhcapqDO4_EQNWErHGoBTaUi1yFh0bgZ3rUL5Zrj3NEnD9HMI5LYST6SREIep_MMtPKvxhSXt4B3oXKPCZZSVoq0BXbRleRk2pupPcf8FoXvb1aSi8bqZnmBJCrhBiCb4FKi1zdmZQCh3-n2fUMP2Qij5kcNTzABxS3K8l4hnR3VpA_ZBWaMTsEeE4OosckIGWcKl2HRelrD0v5Chie-16ppcEsTTyyziTFGfA3QgugfiiVVY57ITpHrdL_s2cSb39-XBOmjzCvRLDA80hXPClg9FAFOgmjvESzoNJ0g6Hkei00lUtm_Qw19KW0TFwJy_Y=", "email_account": "<EMAIL>", "api_id": "********", "api_hash": "e962fdef0c3df6c5f6985d55c2a87fb6"},
    # {"phone": "*************", "account_key": "1BVtsOLYBu6FaSshd0ETPOWK8vJVpKPuZKmeduqztfmBqBludTHNLSAOSqGiIJqz-8wAt_qIe7DK42ZuswCRNPyljQ6PzciYWlp16IYeuzoFDGgWHBrowwD3xanik2YdlawEfY51ubLZr95C4E8ZOJi8M94gmFY8eALQKonoBTpBVqYyA7at6o8wiCedduzxAEg_RhZOIlrzOO3w_V7pQE_eXsYZgTOJOOgK2d7XhTsBT-li8ax5XD850k1KaOUOKTd6d9VZBLqdkA4Kz07jkw1_OPYEKWwa5In3wW2xM48FGhYvf-HLua5zq8Kl3HOe1cQIaRY9tQlKwPiWlXAQxwlyyV5qfOF8=", "email_account": "<EMAIL>", "api_id": "********", "api_hash": "1a8045f40d1b3c44213335a389d12037"},
    # {"phone": "*************", "account_key": "1BVtsOKABu5-3uoIuhbhNInP6bcsy6yqOv7TFYjMNk9kpUW98KyZ9qUBgEV17kuMi18YgMkNPGpOQhZsgx0ynMomooimp8M4UEB0IBujGD6O5Bn0UtPprLMo_D6JeDBJNG3Ylw3n1qdIlW7JkJPZghvOJInoLzfdHt7J0Qse093KmTrPMCj_g-7GnoE8YDB1Z5QhzoSrMsHeT2g9cujon3gjSKHhdm2QOYvDeEdNwgr85EFV4JA77MV0ALQFZ0yKFaMpgdo_Yp0H7DqCFGq5V7I5I_sfq01m99xd8Kmq9wBkjfifd4lmWm9A1qb1rCocVv6m6kF30qqI_Quiv7XlyrYco8EsTTk0=", "email_account": "<EMAIL>", "api_id": "********", "api_hash": "86dd2507fcb41ed80e8bc9860de4af86"},
    # {"phone": "*************", "account_key": "1BVtsOIcBu0Wc86z-5dIqD0E6WyMS22ThBcehCjNPw0ndpcbLVzKX9M9KDoM6su3MeLeyBBNan9foNo-Anyl733eiXXkqmbuVJK4SiA0ejgnxLzlWMvCvtUTABAhAW24BUBJKc-PNv97aiypus0i0atcksN6q1dL7sFHyCJcw17_fH0MUDhwgESH6gXT1JvYf0FTxowGwCGq2c0RjdhKPJat_cxT8J-gvdkWYFQpVz4bXNPxFKesOnipoUq3mibPZ_KF81dES5ZpIeooTM2A2g4tgs_YNkyYOh6ftgDW6i8Te62VF6CCiapm_Lp8ltYeaTwpgvhKxg5vcbeOp595M1gYWmZibM4k=", "email_account": "<EMAIL>", "api_id": "9673789", "api_hash": "8ad4e40fee36d68dbbdce9af87577616"}
]
proxy = get_ipweb_proxy_tg()


async def get_all_dialog():
    for account_info in account_infos:
        string_session = account_info["account_key"]
        developer_info = random.choice(developer_list)
        api_id = developer_info["api_id"]
        api_hash = developer_info["api_hash"]
        client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
        await client.connect()
        is_user = await client.is_user_authorized()

        if not is_user:
            print(">>>>>>>>>>>>>>>>>>>>登录失败")
            client.disconnect()
            continue
        else:
            print("success")
        me = await client.get_me()
        print(me.to_json())
        dialogs = await client.get_dialogs()
        for dialog in dialogs:
            print("=" * 20)
            print(dialog.message.message)


async def kill_all_deives(kill=True):
    for account_info in account_infos:
        string_session = account_info["account_key"]
        developer_info = random.choice(developer_list)
        api_id = developer_info["api_id"]
        api_hash = developer_info["api_hash"]
        client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
        await client.connect()
        is_user = await client.is_user_authorized()
        if not is_user:
            print(">>>>>>>>>>>>>>>>>>>>登录失败")
            client.disconnect()
            continue
        else:
            print("success")

        all_sessions = await client(GetAuthorizationsRequest())
        me = await client.get_me()
        print(all_sessions.to_json())
        print(me.to_json())
        if len(all_sessions.authorizations) != 0:
            print("Another Session    :\tYes")
            for sessions in all_sessions.authorizations:
                print(sessions.to_json())
                SessionHash = sessions.hash
                SessionIp = sessions.ip
                if kill:
                    if SessionHash != 0:
                        result = await client(ResetAuthorizationRequest(hash=SessionHash))
                        print("Session Killed     :\t" + str(SessionIp))
                        print(result)


async def get_login_code():
    for account_info in account_infos:
        proxy = get_ipweb_proxy_tg()
        string_session = account_info["account_key"]
        developer_info = random.choice(developer_list)
        api_id = developer_info["api_id"]
        api_hash = developer_info["api_hash"]
        client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
        await client.connect()
        is_user = await client.is_user_authorized()
        if not is_user:
            print(">>>>>>>>>>>>>>>>>>>>登录失败")
            client.disconnect()
            continue
        else:
            print(f"success:【{account_info['phone']}】")
        me = await client.get_me()

        @client.on(events.NewMessage(pattern=".*Login code.*"))
        async def my_event_handler(event):
            try:
                print(event.message.message)
                await client.disconnect()
                return event.message.message
            except (Exception,) as e:
                print(e)

        await client.run_until_disconnected()


if __name__ == '__main__':
    asyncio.run(get_login_code())
    # asyncio.run(kill_all_deives())
