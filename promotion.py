# -*- coding: utf-8 -*-
# @Time : 2024/2/21 13:33
# @Site : 
# @File : promotion.py
# @Software: PyCharm
import re
import json
import pytz
import httpx
import random
import string
import asyncio
from loguru import logger
from telethon import TelegramClient
from telethon.sessions import StringSession
from datetime import datetime, date, timezone
from utils.developer_infos import developer_list
from telethon.tl.types import MessageActionChatAddUser, User
from utils.config import log_path, error_key, redis_client, failure_key
from telethon.tl.functions.channels import Join<PERSON>hannelRequest, LeaveChannelRequest


class Promotion(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # 待发送群广告的任务key
        self.promotion_key = "tg_spider:promotion:task"
        # 需要验证的群
        self.group_join_verification_key = "tg_spider:group_info_verification"
        # 禁止所有人发言的群
        self.group_disable_sending_messages_key = "tg_spider:group_disable_sending_messages"
        # 账号加入的群组信息
        self.group_join_total_key = "tg_spider:promotion:group_join_total:{}"
        # 账号每日加群数量统计
        self.today_join_count_key = "tg_spider:promotion:today_join_group_count:{}"
        # 发送成功的群组
        self.group_send_succ_key = "tg_spider:promotion:group_send_success:{}"
        # 广告发送成功的key
        self.succ_send_key = "tg_spider:promotion:send_succ:{}"
        # 账号key
        self.account_key = "tg_spider:promotion:account"
        # 广告发送计数
        self.send_total_count_key = "tg_spider:promotion:ad_send_total_count:{}"
        self.send_today_count_key = "tg_spider:promotion:ad_send_today_count:{}"
        self.promotion_today_count_key = "tg_spider:promotion:ad_today_count"
        # 其他的一些key
        self.temp_ban_key = "tg_spider:promotion:temp_ban:{}"
        self.joining_key = "tg_spider:promotion:joining:{}"
        self.sending_key = "tg_spider:promotion:sending:{}"
        self.checking_key = "tg_spider:promotion:checking:{}"
        self.running_key = "tg_spider:promotion:running:{}"
        self.special_account_key = "tg_spider:promotion:special_phone"
        # 群组基础信息的key
        self.group_info_key = "tg_spider:group_info"
        # 账号和代理绑定关系的key
        self.proxy_key = "tg_spider:account_temp_proxy:{}"
        # 加群的cd
        self.join_cd = 60 * 5
        # 加群的最大数量
        self.join_group_max_number = 300
        # 单日加群最大量是50
        self.daily_join_max_number = 50
        # 推广消息的间隔cd
        self.send_cd = 5
        # 推广消息最大的发送量 100  但是发这么多容易封号
        self.daily_max_send = 50
        # 代理url
        self.port_url = "http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1"
        self.developer_list = developer_list

    async def login(self, account_str, proxy, use):
        account_info = json.loads(account_str)
        phone = account_info.get("phone")
        try:
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {proxy}")
            string_session = account_info.get("account_key")
            developer_info = random.choice(self.developer_list)
            api_id = developer_info["api_id"]
            api_hash = developer_info["api_hash"]
            client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
            try:
                await client.connect()
            except OSError as e:
                logger.error(e)
                return
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"推广账号【{phone}】登录失败, 被封号了,登录的api信息是 【{developer_info}】"
                logger.error(f"【{phone}】被封号了")
                await redis_client.srem(self.special_account_key, phone)
                group_join_total_key = self.group_join_total_key.format(phone)
                await redis_client.delete(group_join_total_key)
                await redis_client.srem(self.account_key, account_str)
                await redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                await redis_client.set(self.running_key.format(phone), use)
                return client
        except (Exception,) as e:
            logger.error(e)
            if "two different IP" in str(e):
                joining = self.joining_key.format(phone)
                sending = self.sending_key.format(phone)
                checking = self.checking_key.format(phone)
                if not await redis_client.get(joining) and await redis_client.get(sending) and await redis_client.get(checking):
                    await redis_client.delete(self.proxy_key.format(phone))
                await redis_client.srem(self.special_account_key, phone)
                group_join_total_key = self.group_join_total_key.format(phone)
                await redis_client.delete(group_join_total_key)
                await redis_client.srem(self.account_key, account_str)
                error_mess = f"推广账号【{phone}】【{use}】登录失败"
                await redis_client.sadd(error_key, error_mess)

    async def join_group(self):
        """
        加群
        :return:
        """
        while True:
            # 先判断账号得任务状态
            await asyncio.sleep(random.randint(1, 2))
            accounts = await redis_client.smembers(self.account_key) or "[]"
            accounts_str = random.choice(list(accounts))
            account_info = json.loads(accounts_str)
            phone = account_info.get("phone")
            specific_phone = False
            joining = self.joining_key.format(phone)
            if await redis_client.get(joining):
                continue
            else:
                await redis_client.set(joining, value=phone, ex=self.join_cd)
            # 再判断账号加群的状态
            group_join_total_key = self.group_join_total_key.format(phone)
            group_join_total = await redis_client.get(group_join_total_key) or 0
            today_join_count_key = self.today_join_count_key.format(phone)
            today_join_count = await redis_client.get(today_join_count_key) or 0
            logger.info(f"【{phone}】 当前加群总量是【{group_join_total}】,今日加群【{today_join_count}】")
            # 如果账号已经加入的群组在最大加群数量以下,并且今日加群次数少于指定值,就可以进行加群
            if int(group_join_total) < self.join_group_max_number and int(today_join_count) < self.daily_join_max_number:
                # 开始登录账号
                proxy_key = self.proxy_key.format(phone)
                proxy = await self.get_proxy(proxy_key)
                client = await self.login(accounts_str, proxy, "加群")
                if not client:
                    continue
                # 获取到所有的待加群任务
                if specific_phone:
                    promotion_key = "tg_spider:promotion:special_task"
                else:
                    promotion_key = self.promotion_key
                join_succ = True
                group_link = await redis_client.srandmember(promotion_key)
                try:
                    # 判断加群是否成功的
                    is_disable_send = await redis_client.sismember(self.group_disable_sending_messages_key, group_link)
                    is_verification = await redis_client.sismember(self.group_join_verification_key, group_link)
                    is_failure = await redis_client.sismember(failure_key, group_link)
                    if not group_link or is_verification or is_failure or is_disable_send:
                        await redis_client.srem(promotion_key, group_link)
                        continue
                    entity = await client.get_entity(group_link)
                    if isinstance(entity, User):
                        await redis_client.srem(promotion_key, group_link)
                        continue
                    if entity.default_banned_rights and entity.default_banned_rights.send_messages:
                        await redis_client.sadd(self.group_disable_sending_messages_key, group_link)
                        await redis_client.srem(promotion_key, group_link)
                        logger.warning(f"【{group_link}】group_disable_sending_messages")
                        continue
                    if entity.broadcast:
                        logger.warning(f"【{group_link}】is_broadcast")
                        await redis_client.srem(promotion_key, group_link)
                        continue
                    # 这些群组的附件需要存储,需要单独记录到一个key里面,用于download_media方法里面判断使用
                    logger.info(f"用户【{phone}】开始加入【{group_link}】,加入次数是【{today_join_count}】")
                    join_status = await client(JoinChannelRequest(entity))
                    # 今日加群计数
                    if today_join_count == 0:
                        await redis_client.set(today_join_count_key, value="1", ex=24 * 60 * 60)
                    else:
                        await redis_client.incr(today_join_count_key)
                    updates = join_status.updates
                    # 加入成功后,需要监控后面的几条消息,看看是否有验证
                    if updates:
                        message = updates[-1]
                        action = message.message.action
                        message_id = message.message.id
                        if isinstance(action, MessageActionChatAddUser):
                            # 判断加群后最新的3条消息,看看是否有弹出来验证码
                            for i in range(1, 4):
                                max_number = 2
                                while max_number > 0:
                                    try:
                                        verification_message = await client.get_messages(entity, ids=message_id + i)
                                        max_number -= 1
                                        if verification_message:
                                            content = verification_message.message
                                            if content.count("验证") or content.count("限制") or content.count("封禁") or content.count("verification"):
                                                join_succ = False
                                            break
                                        else:
                                            await asyncio.sleep(0.2)
                                            continue
                                    except:
                                        await asyncio.sleep(0.2)
                                        continue
                                if join_succ is False:
                                    break
                    # 如果成功加入,并且没有验证码
                    if join_succ:
                        await redis_client.incr(group_join_total_key)
                        if specific_phone:
                            await redis_client.sadd("tg_spider:promotion:special_task_id", entity.id)
                    else:
                        await redis_client.sadd(self.group_join_verification_key, group_link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot find any entity corresponding" in str(e):
                        logger.error(f"【{group_link}】群不存在")
                        await redis_client.srem(promotion_key, group_link)
                        await redis_client.sadd(failure_key, group_link)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{group_link}】用户尝试加入的聊天已过期，不再有效")
                        await redis_client.sadd(failure_key, group_link)
                        await redis_client.srem(promotion_key, group_link)
                    elif "A wait of " in str(e):
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            await redis_client.set(joining, value=phone, ex=wait_time_int)
                        continue
                    elif "The channel specified is private and you lack permission to access it. Another reason may be that you" in str(e):
                        await redis_client.sadd(self.group_join_verification_key, group_link)
                        await redis_client.srem(promotion_key, group_link)
                        continue
                    elif "Cannot send requests while disconnected" in str(e):
                        await redis_client.sadd(promotion_key, group_link)
                        continue
                    else:
                        await redis_client.sadd(promotion_key, group_link)
                finally:
                    await client.disconnect()
                    await redis_client.delete(self.running_key.format(phone))
                    today_join_count = await redis_client.get(today_join_count_key) or 0
                    group_join_total = await redis_client.get(group_join_total_key) or 0
                    logger.info(f"【{phone}】 加群任务结束,当前加群总量是【{group_join_total}】,今日加群【{today_join_count}】")
            else:
                detected_key = "tg_spider:promotion:group_join_number_count:{}".format(phone)
                if not await redis_client.get(detected_key):
                    proxy_key = self.proxy_key.format(phone)
                    proxy = await self.get_proxy(proxy_key)
                    client = await self.login(accounts_str, proxy, "检测群加入数量")
                    try:
                        dialogs = await client.get_dialogs()
                        groups = 0
                        for dialog in dialogs:
                            if dialog.is_group is False:
                                if dialog.is_channel:
                                    await client(LeaveChannelRequest(dialog.entity))
                                    await asyncio.sleep(2)
                            else:
                                groups += 1
                        await redis_client.set(group_join_total_key, groups)
                        await redis_client.set(detected_key, phone, ex=24 * 60 * 60)
                        logger.info(f"【{phone}】已经加入了【{groups}】个群组")
                    except (Exception,) as e:
                        logger.error(e)
                    finally:
                        await client.disconnect()

    async def send_ad_messages(self):
        """
        在群组里面发送信息
        :return:
        """
        while True:
            try:
                await asyncio.sleep(random.randint(1, 2))
                accounts = await redis_client.smembers(self.account_key) or "[]"
                accounts_str = random.choice(list(accounts))
                account_info = json.loads(accounts_str)
                phone = account_info.get("phone")
                # 判断当前是不是能发送
                succ_send_key = self.succ_send_key.format(phone)
                success_send = await redis_client.get(succ_send_key)
                # 判断当前加群数量,数量太少不发送
                group_join_total_key = self.group_join_total_key.format(phone)
                group_join_total = await redis_client.get(group_join_total_key) or 0
                # 判断今日发送消息次数
                send_today_count_key = self.send_today_count_key.format(phone)
                sent_today_count = await redis_client.get(send_today_count_key) or 0
                if success_send or int(group_join_total) < 41 or int(sent_today_count) >= self.daily_max_send:
                    continue
                sending_key = self.sending_key.format(phone)
                if await redis_client.get(sending_key):
                    continue
                else:
                    await redis_client.set(sending_key, value="发送消息", ex=24 * 60 * 60)
                # 登录账号
                proxy_key = self.proxy_key.format(phone)
                proxy = await self.get_proxy(proxy_key)
                client = await self.login(accounts_str, proxy, "发送消息")
                if not client:
                    continue
                # 开始发送信息
                dialogs = await client.get_dialogs()
                banned_sending_groups_key = "tg_spider:promotion:banned_sending_groups:{}".format(phone)
                # 遍历所有的群组聊天框
                specific = await redis_client.sismember(self.special_account_key, phone)
                # 账号消息发送总量的key
                send_total_count_key = self.send_total_count_key.format(phone)
                # today
                today = date.today()
                for dialog in dialogs:
                    if dialog.is_group is False:
                        continue
                    if int(sent_today_count) >= self.daily_max_send:
                        await redis_client.set(succ_send_key, phone, ex=24 * 60 * 60)
                        break
                    group_id = dialog.entity.id
                    group_send_succ_key = self.group_send_succ_key.format(str(group_id) + str(phone))
                    if await redis_client.sismember(banned_sending_groups_key, group_id) or await redis_client.get(group_send_succ_key):
                        continue
                    group_link = await redis_client.hget(self.group_info_key, group_id)
                    # 获取到当前账号发送总次数
                    try:
                        # 发送干扰信息
                        if specific:
                            specific_message = await redis_client.get("tg_spider:promotion:specific_message")
                            message_results = await client.send_message(dialog.entity, specific_message)
                        else:
                            # 生产随机的消息语句
                            message_list = ["开启全新聊天体验，下载高安全性社交软件Tsoul，畅所欲言！", "无广告，高安全保障！", "超级检索，快速找到大量目标群组和用户！", "加入Tsoul，与好友随时随地畅享畅聊时光。立即下载，畅享沟通快乐！", "超多小姐姐", "查找附近人", "探索无限可能，尽在Tsoul！", "顺畅聊天", "私密聊天", "端对端加密"]
                            sonnector_str = random.choice(string.punctuation)
                            random.shuffle(message_list)
                            sonnector_str.join(sonnector_str)
                            sonnector_str += "\n"
                            message = f'{sonnector_str.join(message_list[0:random.randint(2, len(message_list))])}{sonnector_str} 点击下载: www.tsoul.info%2F'
                            message_results = await client.send_message(dialog.entity, message, link_preview=True)
                        if not message_results:
                            continue
                        if specific:
                            await redis_client.incr(f"tg_spider:promotion:specific_ad_send_today_count:{today}")
                            await redis_client.incr("tg_spider:promotion:specific_ad_send_total_count")
                        # 当前账号今日发言和账号总发言数的计数
                        if sent_today_count == 0:
                            await redis_client.set(send_today_count_key, 1, ex=24 * 60 * 60)
                        else:
                            await redis_client.incr(send_today_count_key)
                        sent_today_count = await redis_client.get(send_today_count_key) or 0
                        await redis_client.incr(send_total_count_key)
                        send_total_count = await redis_client.get(send_total_count_key)
                        # 所有账号发言计数
                        promotion_today_count = await redis_client.get(self.promotion_today_count_key) or 0
                        if promotion_today_count == 0:
                            await redis_client.set(self.promotion_today_count_key, 1, ex=24 * 60 * 60)
                        else:
                            await redis_client.incr(self.promotion_today_count_key)
                        await redis_client.set(group_send_succ_key, group_id, ex=24 * 60 * 60)
                        # 发送完成了休眠
                        await asyncio.sleep(self.send_cd)
                        if specific:
                            logger.success(f"【{phone}】sent specific successfully! todayCount:【{sent_today_count}】! groupLink: 【{group_link}】,time: 【{message_results.date.replace(tzinfo=timezone.utc).astimezone(tz=None)}】.")
                        else:
                            logger.success(f"【{phone}】sent successfully! todayCount:【{sent_today_count}】! phoneTotal:【{send_total_count}】! groupLink: 【{group_link}】,time:【{message_results.date.replace(tzinfo=timezone.utc).astimezone(tz=None)}】.")
                    except (Exception,) as e:
                        logger.error(e)
                        # 发送太快,需要等待的情况
                        if "A wait of " in str(e):
                            wait_time = re.search("\d+", str(e))
                            if wait_time:
                                wait_time_int = int(wait_time.group())
                                if wait_time_int > 180:
                                    break
                                else:
                                    await asyncio.sleep(wait_time_int)
                        # 不能在群组发言的情况
                        elif "You can't write in this chat" in str(e) or "banned from it" in str(e) or "You're banned from sending messages in supergroups/channels" in str(e):
                            await client(LeaveChannelRequest(dialog.entity))
                            await redis_client.decr(group_join_total_key)
                            logger.warning(f"【{phone}】haveBeen【{group_link}】Forbidden")
                            await redis_client.sadd(self.promotion_key, group_link)
                            await asyncio.sleep(self.send_cd)
                        # 发送消息已经上限的情况
                        elif "CHAT_SEND_PLAIN_FORBIDDEN" in str(e):
                            logger.warning(f"【{phone}】made too many requests.  total of sent today【{sent_today_count}】")
                            await redis_client.set(succ_send_key, phone, ex=24 * 60 * 60)
                            break
                await client.disconnect()
                await redis_client.delete(sending_key)
                await redis_client.set(succ_send_key, sent_today_count, ex=24 * 60 * 60)
                await redis_client.delete(self.running_key.format(phone))
                logger.info(f"【{phone}】 total of 【{sent_today_count}】 messages were sent today")
            except (Exception,) as e:
                logger.error(e)

    async def status_check(self, client, phone):
        # 开始判断账号状态
        entity = await client.get_entity("https://t.me/SpamBot")
        async with client.conversation(entity, timeout=10) as conv:
            await conv.send_message("/start")
            while True:
                await asyncio.sleep(3)
                response = await conv.get_response()
                messages = response.message
                if messages.startswith("I’m afraid some Telegram users found your messages annoying and forwarded them to our team of moderators for inspection"):
                    banned_date = re.search("(?<=until ).*?(?=\.)", messages).group()
                    date_format = "%d %b %Y, %H:%M %Z"
                    utc_timezone = pytz.timezone('UTC')
                    local_timezone = pytz.timezone('Asia/Shanghai')
                    date_time = datetime.strptime(banned_date, date_format)
                    date_time_local = utc_timezone.localize(date_time).astimezone(local_timezone)
                    now = datetime.now()
                    # 计算时间差
                    delta = int(date_time_local.timestamp() - now.timestamp())
                    error_mess = f"账号【{phone}】被官方禁言至【{date_time_local.strftime('%Y-%m-%d %H:%M:%S')}】"
                    await redis_client.sadd(error_key, error_mess)
                    await redis_client.set(self.succ_send_key.format(phone), phone, ex=delta)
                    await redis_client.set(self.temp_ban_key.format(phone), phone, ex=delta)
                    break
                elif messages.count("Good news, no limits are currently applied to your account"):
                    break
            await conv.cancel_all()

    async def account_status_check(self):
        while True:
            # 随机登录一个账号
            await asyncio.sleep(random.randint(1, 3))
            accounts = await redis_client.smembers(self.account_key) or "[]"
            accounts_str = random.choice(list(accounts))
            account_info = json.loads(accounts_str)
            phone = account_info.get("phone")
            succ_send_key = self.succ_send_key.format(phone)
            checking = self.checking_key.format(phone)
            is_checking = await redis_client.get(checking)
            if is_checking:
                continue
            else:
                await redis_client.set(checking, value=phone, ex=60 * 60 * 4)
            proxy_key = self.proxy_key.format(phone)
            proxy = await self.get_proxy(proxy_key)
            client = await self.login(accounts_str, proxy, "检测封号状态")
            if not client:
                continue
            try:
                # 开始判断账号状态
                user_link = "https://t.me/SpamBot"
                entity = await client.get_entity(user_link)
                async with client.conversation(entity, timeout=10) as conv:
                    await conv.send_message("/start")
                    while True:
                        await asyncio.sleep(3)
                        response = await conv.get_response()
                        messages = response.message
                        if messages.startswith("I’m afraid some Telegram users found your messages annoying and forwarded them to our team of moderators for inspection"):
                            banned_date = re.search("(?<=until ).*?(?=\.)", messages).group()
                            date_format = "%d %b %Y, %H:%M %Z"
                            utc_timezone = pytz.timezone('UTC')
                            local_timezone = pytz.timezone('Asia/Shanghai')
                            date_time = datetime.strptime(banned_date, date_format)
                            date_time_local = utc_timezone.localize(date_time).astimezone(local_timezone)
                            now = datetime.now()
                            # 计算时间差
                            delta = int(date_time_local.timestamp() - now.timestamp())
                            error_mess = f"账号【{phone}】被官方禁言至【{date_time_local.strftime('%Y-%m-%d %H:%M:%S')}】"
                            await redis_client.sadd(error_key, error_mess)
                            await redis_client.set(succ_send_key, phone, ex=delta)
                            await redis_client.set(self.temp_ban_key.format(phone), phone, ex=delta)
                            break
                        elif messages.count("Good news, no limits are currently applied to your account"):
                            break
                    await conv.cancel_all()
            except (Exception,) as e:
                logger.error(e)
            finally:
                await client.disconnect()
                await redis_client.delete(self.running_key.format(phone))

    async def get_proxy(self, proxy_key, max_number=10):
        proxy_str = await redis_client.get(proxy_key)
        if proxy_str:
            proxy = json.loads(proxy_str)
            return proxy
        while max_number > 0:
            try:
                await asyncio.sleep(0.5)
                resp = httpx.get(self.port_url, headers={"Token": "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"})
                username = resp.json()["data"][0].split(":")[0]
                password = resp.json()["data"][0].split(":")[1]
                proxy = {
                    'proxy_type': 'http',  # (mandatory) protocol to use (see above)
                    'addr': "gate1.ipweb.cc",  # (mandatory) proxy IP address
                    'port': 7778,  # (mandatory) proxy port number
                    'username': username,  # (optional) username if the proxy requires auth
                    'password': password,  # (optional) password if the proxy requires auth
                    'rdns': True  # (optional) whether to use remote or local resolve, default remote
                }
                await redis_client.set(proxy_key, json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
                return proxy
            except (Exception,) as e:
                max_number -= 1
                logger.error(e)
                await asyncio.sleep(1)

    async def start_task(self):
        while True:
            try:
                tasks = []
                tasks.extend([self.send_ad_messages() for _ in range(5)])
                tasks.extend([self.join_group() for _ in range(5)])
                tasks.extend([self.account_status_check() for _ in range(5)])
                await asyncio.gather(*tasks)
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 10)


if __name__ == '__main__':
    promotion = Promotion()
    asyncio.run(promotion.start_task())
    # asyncio.run(promotion.count_the_number_of_groups_added())
