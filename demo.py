#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/9/9 10:08
# @Site    : 
# @File    : demo.py
# @Software: PyCharm
import json
import time

from gmssl import sm3, sm4
from binascii import a2b_hex

# 请求头
headers = {"x-token": "4765aa5184b260e0", "x-timestamp": str(int(time.time() * 1000))}
parmas = {"phone": "86123123123123"}
body = headers["x-token"] + "14fb3f6f795ba5c6" + headers["x-timestamp"]
body_str = json.dumps(body)
# 通过sms3计算出 加密和解密使用需要使用的key
msg_list = [i for i in bytes(body_str.encode('UTF-8'))]
digest = sm3.sm3_hash(msg_list)
print("sm3加密后：", digest)
requests_key = a2b_hex(digest[0:32])
respones_key = a2b_hex(digest[32:])


def sm4_encryption(key, text):
    crypt_sm4.set_key(key, sm4.SM4_ENCRYPT)
    encrypt_value = crypt_sm4.crypt_ecb(text)
    return encrypt_value.hex()


def sm4_decryption(key, text):
    crypt_sm4.set_key(key, sm4.SM4_DECRYPT)
    decrypt_value = crypt_sm4.crypt_ecb(text)
    return decrypt_value.decode()


if __name__ == '__main__':
    crypt_sm4 = sm4.CryptSM4()
    requests_encryption_parmas = sm4_encryption(requests_key, json.dumps(parmas).encode())
    requests_decryption_parmas = sm4_decryption("f795e34c94384805".encode(), a2b_hex("6a9abf07a216dbb5a1518f1a49ef505037ff4968c8366b44ba37a1f35125089e"))
    print("请求参数加密结果:  ", requests_encryption_parmas)
    print("请求参数解密结果:  ", requests_decryption_parmas)
