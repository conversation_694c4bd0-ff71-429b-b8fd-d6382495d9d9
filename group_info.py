# -*- coding: utf-8 -*-
import re
import time
import json
import aioredis
from loguru import logger
from telethon import TelegramClient
from telethon.sessions import StringSession

from utils.crawl_tools import get_group_info, get_machine_ip
from utils.config import redis_host, redis_port, redis_password, api_id, api_hash, log_path, error_key, failure_key


class GroupInfo(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.redis_client = aioredis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)

        self.link_key = "tg_spider:group_info_links"
        self.success_key = "tg_spider:group_info_crawl_success"

        self.account_key = "tg_spider:group_info_account:{}"
        self.requests_num_key = "tg_spider:group_info_requests_num:{}"
        self.group_info_key = "tg_spider:group_info:{}"

        self.max_requests_number = 150

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群基础信息采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except:
            error_mess = f"群基础信息采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            logger.error(error_mess)
            await self.redis_client.sadd(error_key, error_mess)

    async def load_link(self):
        import pandas as pd
        df = pd.read_excel("./file/data.xls")
        df.fillna("", inplace=True)
        links = list(df["链接"])
        for link in links:
            fail = await self.redis_client.sismember(failure_key, link)
            if fail:
                continue
            succ = await self.redis_client.sismember(self.success_key, link)
            if succ:
                continue
            await self.redis_client.sadd(self.link_key, link)

    async def group_info(self, account_info, machine_ip):
        try:
            phone = account_info.get("phone")
            client = await self.login(account_info, machine_ip)
            assert client
            requests_num_key = self.requests_num_key.format(phone)
            number_of_requests = await self.redis_client.get(requests_num_key)
            # 判断账号请求次数是否已经达到上限
            if not str(number_of_requests).isdigit():
                number_of_requests = 0
            async with client:
                while int(number_of_requests) < self.max_requests_number:
                    try:
                        scard = await self.redis_client.scard(self.link_key)
                        if scard < 1:
                            break
                        link = await self.redis_client.spop(self.link_key)
                        # 判断重复链接
                        member = await self.redis_client.sismember(self.success_key, link) or await self.redis_client.sismember(failure_key, link)
                        if member:
                            continue
                        # 判断登录状态
                        is_user = await client.is_user_authorized()
                        if not is_user:
                            error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                            logger.error(error_mess)
                            await self.redis_client.sadd(error_key, error_mess)
                            break
                        logger.info(f"开始采集群组信息:【{link}】")
                        now_time = time.strftime("%F", time.localtime())
                        # 获取群组信息
                        entity = await client.get_entity(link)
                        if number_of_requests == 0:
                            await self.redis_client.set(requests_num_key, value="1", ex=24 * 60 * 60)
                        else:
                            await self.redis_client.incr(requests_num_key)
                        channel_id = entity.id
                        channel_name = entity.title
                        logger.info(f"开始采集群组详情,群组名字:【{channel_name}】,群组id:【{channel_id}】")
                        group_info_key = self.group_info_key.format(channel_id)
                        await self.redis_client.sadd(group_info_key, link)
                        await get_group_info(client, link, entity)
                        await self.redis_client.sadd(self.success_key, link)
                    except (Exception,) as e:
                        logger.exception(e)
                        if "No user has" in str(e) or "Nobody is using this username" in str(e):
                            logger.error(f"【{link}】群不存在")
                            await self.redis_client.sadd(failure_key, link)
                        elif "The chat the user tried to join has expired" in str(e):
                            logger.error(f"【{link}】用户尝试加入的聊天已过期，不再有效")
                            await self.redis_client.sadd(failure_key, link)
                        elif "A wait of " in str(e):
                            link = await self.redis_client.sadd(self.link_key, link)
                            wait_time = re.search("\d+", str(e))
                            if wait_time:
                                wait_time_int = int(wait_time.group())
                                if wait_time_int < 100:
                                    await asyncio.sleep(wait_time_int)
                                else:
                                    number_of_requests = self.max_requests_number
                    finally:
                        if int(number_of_requests) < self.max_requests_number:
                            number_of_requests = await self.redis_client.get(requests_num_key) or 0
                        logger.info(f"当前请求次数 {number_of_requests}")
        except (Exception,) as e:
            logger.exception(e)

    async def bind_account(self):
        ip = get_machine_ip()
        accounts = [{'phone': '***********', 'account_key': '1AZWarzMBuyRQIephMu_uuheGBB4nZASus0loWuOlOfDyvuQnMQV0h2DIIvNL6o30yPhhvKOqJUzXHh7juhA4tTi2Df6WFis7DKzAGmunFHPZatptI1EXNsIl7Q3EoJqGslD8osZywQ8Nsh0_GznlUPpEzLUbuOxeazztV1b1uSEprTuw6QqiQeotTkB62pXORR7O1cAYdMgSgfitMusTVB4coAKyqOnXFuCGbfN24dK_cl1UOIuPyjpe3zuFHd8PVXAI1-SBL60zBieLXn4v19lJ23-DNYJ1ul7djT5O8TquPRPF7sgtND05nrKQcWfmeeOIbWyOYepGs8sZPKecWHilnHPsCtM='},
                    {'phone': '***********', 'account_key': '1AZWarzkBu8C_wQzm_SRhyxHHuZuE086yyPqA17q3K89m_1AYzFBFC5W_uG2pDmUb1S2ipNvo5MNizlmdpw6dOak5XJ28CuCIKkF6H6dV94ipPvnYnVyODRUU7HWh74MTxpqv2t3IgGyM2GWzlX8eeaD_QVcUNNfNUK_e7aY1RWza0jdAlP0FjM2gqBTeeMuGYIRAd2VsvNCRJqodCFCXCdI9zyAaFFDqzKA00Gk4rg_ffFAbNNElOCxHk0GmX4480AwmkHE91lkdl8VpTcoiBja1gC8riz99fMs9jWgzAVyyEzuubwntQNvR2BjNN5TFL3EgaX8O5mrDHyezQ3K6L3gIGgO2Nlk='},
                    {'phone': '***********', 'account_key': '1AZWarzQBuxkgYVVruRiKc2zoJ7jvGpGfi-d_DcF6KFRruAEPUrbXqiTvq-M6--iDlO9iCfBM-U_Lx8Zqw8lfttMdQZ629qwlqeFhj1Dp1yR8Gz3jyS2Ua-W6Y9b2LuZKMGsfiC4B1H8F8mavtpKGY1bWAqA-Pc7nihR6hyc4uh3o4IXzedBclRPYoM-l1YWXn9jAcZNwLtpxt_xQKpfduAiyRqLdFWw32Z6ompsY0urjZSc6d5FH8jaAdzLgAef2ieUDPHBtlq9fXhyycULTqYof8rQQmbavrJ2iPD26D3TYS8Y7zCEvTtrsinjYpwTqarhRP9qlZN_zW31RmonrI28H_6DWwiU='},
                    {'phone': '***********', 'account_key': '1AZWarzwBu0kQtC-sy892fMvG4KWHJmhmrbFFuiUXoyg_CbZwkTIcbcPPIJCvTviF2xczi5P9WAPyYaUJHr-pmY_n-CtYFCMyPR8Oehj9ZGa66AfR3xfdqbQuqesAWqYJiE6Kc-orZXkwKPvpAn5SkUxUcWmT06nUy2SCU2mlGtvV9NqsGFwO4bkF0HyWBO1P8xevKwPjzI-8_cATfK5MbIze3YgDxSsW4JbF2kFhKs4a1AKb7AzeUyHntfr5HRZPMR2gRBrah2Bfa27v7oxQWXWCAcWQYuFTHr11latyTEbwdCd6M_lLQ_cWXQWiMBSUodOSJEY9JTbEAnMLVoJaEGSIvw8noXs='},
                    {'phone': '***********', 'account_key': '1AZWarzwBu2Rb8KZUttaIkgz3LugrMTmYppQpLu780BzifJNp2MNu5VRlHfCz-o6Jg6x4zvU2pwZQfAmfAB6W0TEQRZqRm6AywzokDzmssXyykvX8Eg_Xb3X2TBLT77bQOL39CWN5dspqvZn-kIWrD_VzmYzaw_5E2nCiv7Zcx2Nw-KqkUmHcZEyhAjZR5T7Xt5RRbwSJxB6r-OSJK9vv8xUfAZLkP0Vul-W0bRJtiplV0WHM9vLWTQgxJgg_IYMWaGSfeK4tvInYOsxXG3A0iRrgsSpk8bP0EsNwMDj4r9KXlN24ultw-1-TrWj3NLLJERk-oXvW9ximhpexi43Hswx2wUkNQw8='},
                    {'phone': '***********', 'account_key': '1AZWarzoBu60MhSUX21XKBcfpykacQN5xjQmwAb8zXGSMwZByU5vVqHa04RP-4UuHwgOfhMnlHKLogU-vnf4Afr3pPt-1lQ4ZucxBcDJf5JBbS1yfafd1Z8yf9UDOdUfFTqssn_kQy7YO6TaQl2tKW2Fspsc1eQdp8FFpULavLurjO6iH8J9U9qOy1aSTTGOX2OpyUe3_wzLld9BlguvIVAv4VR-3583rz1vMsxNj_InKENZEWAvSmmzZIl6SNUcNrgvv26HtlDQEN3LAUWbObsMcg4ypPmFmqfOUtd47A-F1oYPPxGdSO15PdEtWqrESdGvyoWIrksR7Qu8MiWmza_Dvt1kHzjQ='},
                    {'phone': '***********', 'account_key': '1AZWarzYBuy6oKPT6nOPYEqG14TD0OaYY-sBw5cs97Y6N6VEJhKafogajA1gvBwVtbkNkyGqtiiGGq34syTR3-4SoYnwlY5Ijb6pUSUsJxHUIKxGYdN7omeXGf7cv8Yzg7kgaKpYLNOpy9VLSXxG-NSEk88nNJi4JmYwwMwte9gvLqgOZkHMAUeVSzSXRKaZO6gLtpw9M4zbolGEUpNvAK1JhfOc7jT6E9LaLZ6Sfpdn78gLR50Fj-ezCFPBC9hviieIPpKmBgdlp5bIQMhqhHTxgZRfv2VY-Nf2tEOICvZPeSBGC-5EzTRF8PVq09IDdUixCKDDwELnVn8IUcrBU9PjvMr-Tx0Y='},
                    {'phone': '***********', 'account_key': '1AZWarzQBu0ENw-5iuhCYTpM2ATWXzwoj9umbAD_sr7f66AQCacPTDLUeLHrh3Zlto65T3CQrJhbLAM8ltX5XRl1lDMCu0iL0ZPs1FLnE3dV1bG-pb4Vsgg45MsRijvLc5H_sjubeAwhlAk4uD9gzkuyzH_fjhw0DNQHs9UidaE96NPIO5jl_KQznZmAGgbNNUtd4H9lrFw0NxEJdLL-2ATHsEwDkFkvws5syXWALxFxF23nWq8IbT0uT8IxYDf_hAf4vUyglOI_F60Hxg6EVKV_64LgFbI9hqGfXFU5izCN99GpPesZRxnkC9OLQ2O_zd468PfBJInM-DVJDsQJpXDhkdlXgVBc='},
                    {'phone': '***********', 'account_key': '1AZWarzQBu6vlJiHTQ7NW74Tpmhe0HqDBx6P8m-7IwzgEzX_QZccqaexHEa49Co34dJWbPq42qSuceNHvKR5OQ7TE7SonejVUxR0glGYhE4oANfDvznda-65KKeiiAa1Eh3nns_D0pL09yKPbFJA0uiMFV_WPcQc3Ii3dvdytRwce-n6NsSGq6Fr-3X_OqsIv62oO8FliehOXoIxnxFZiCqBUvRZ0y9mq0xWICAjhmTDhMlmD-XDXsfitflSuiPBKa1Wjc6-CCxoheBqeLxKyc_C1AAr2DcO1De7LDBGhY2Ab7-5omWXqAebNDTSE5be84DnLV136BbzEjgPbQv3DGS5IF-etIG0='},
                    {'phone': '***********', 'account_key': '1AZWarzUBuzrIvqbFzQQ_Rb1qZRI5UbDO13-4kVUxA6azjj4LYmj2WALXr5KZD_d0hgRhcv1hKtBiI6jSilNB0u8oFHJwnKFfdhFpT30klBEcwzJAcwQSCASj59ax672rp48GubBp1rKi1pfy9irnAudW5kZgR-B5dqD3zcN2UzCq9K6iUVoFyqERIpEY2Gnm4I8AfGciF4-DTiL4YFByXzVXSvH2J6RhQNX2OXSgLJOIo0rCLi0RF-WXr9uVnJZAPsyxiutmUo_7NN3SvLxPQmayHeF1XKtngEiWuOWBJSkEwo5eMstjZdWl3lhYsNoKN-mrzqzVHM7HSRlynj6vsw-sHnbqiDc='}]
        await self.redis_client.set(self.account_key.format(ip), json.dumps(accounts))

    async def start_crawler(self):
        while True:
            try:
                machine_ip = get_machine_ip()
                account_key = self.account_key.format(machine_ip)
                account_list = await self.redis_client.get(account_key) or "[]"
                accounts = json.loads(account_list)
                if len(accounts) == 0:
                    logger.warning(f"【{machine_ip}】下没有绑定的账号")
                for account_info in accounts:
                    phone = account_info.get("phone")
                    requests_num_key = self.requests_num_key.format(phone)
                    number_of_requests = await self.redis_client.get(requests_num_key) or 0
                    if int(number_of_requests) < self.max_requests_number:
                        try:
                            await self.group_info(account_info, machine_ip)
                        except (Exception,) as e:
                            logger.exception(e)
                    else:
                        logger.warning(f"【{phone}】今日请求次数已经消耗完了,共请求了【{number_of_requests}】次")
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 60)


if __name__ == '__main__':
    import asyncio

    crawl_group_info = GroupInfo()
    # asyncio.run(crawl_group_info.bind_account())
    # asyncio.run(crawl_group_info.load_link())
    asyncio.run(crawl_group_info.start_crawler())
