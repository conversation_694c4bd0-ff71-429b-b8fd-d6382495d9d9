# -*- coding: utf-8 -*-
import os
import re
import time
import json
import httpx
import random
import asyncio
import datetime
from loguru import logger
from telethon import TelegramClient
from telethon.sessions import StringSession
from utils.crawl_tools import upload_file_to_oss
from utils.developer_infos import developer_list
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.types import InputPhoneContact, UserStatusOffline, UserStatusOnline
from telethon.tl.functions.contacts import ImportContactsRequest, DeleteContactsRequest
from utils.config import log_path, error_key, oss_base_path, save_url, user_kafka_key, redis_client


class searchUserInfoByPhone(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.task_key = "tg_spider:phone_search:task"
        self.succ_key = "tg_spider:phone_search:succ"
        # 账号每日搜索的次数统计
        self.request_count_today = "tg_spider:phone_search:today_count:{}"
        # 账号池
        self.account_key = "tg_spider:phone_search:account"
        # 账号搜索次数总数的key
        self.phone_total_key = "tg_spider:phone_search:phone_total:{}"
        self.running_key = "tg_spider:phone_search:running:{}"
        self.results_key = "tg_spider:phone_search:results"
        self.config_key = "tg_spider:phone_search:config"
        # 被禁止搜索的key
        self.banned_key = "tg_spider:temp_banned:{}"
        # 账号和代理绑定关系的key
        self.proxy_key = "tg_spider:account_temp_proxy:{}"
        # 搜索的间隔
        self.search_cd = 0
        # 每日最大的搜索次数
        self.max_daily_searche_number = 0
        # 代理url
        self.port_url = "http://api.ipweb.cc:8004/api/agent/account2?country=HK&times=90&limit=1"
        self.developer_list = developer_list

    async def login(self, account_str, proxy):
        account_info = json.loads(account_str)
        phone = account_info.get("phone")
        try:
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {proxy}")
            string_session = account_info.get("account_key")
            developer_info = random.choice(self.developer_list)
            api_id = developer_info["api_id"]
            api_hash = developer_info["api_hash"]
            client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
            try:
                await client.connect()
            except OSError as e:
                logger.error(e)
                return
            is_user = await client.is_user_authorized()
            if not is_user:
                phone_total = await redis_client.get(self.phone_total_key.format(phone))
                error_mess = f"手机号批量搜索账号【{phone}】被封号了，搜索总次数【{phone_total}】"
                logger.warning(error_mess)
                await redis_client.srem(self.account_key, account_str)
                await redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            logger.warning(account_str)
            if "two different IP" in str(e):
                await redis_client.srem(self.account_key, account_str)
                error_mess = f"手机号搜索账号【{phone}】登录失败"
                await redis_client.sadd(error_key, error_mess)
            return

    async def search_user_info_by_phone(self):
        """
        :return:
        """
        while True:
            # 先判断账号不在登录状态
            today = datetime.date.today()
            accounts = await redis_client.smembers(self.account_key) or "[]"
            account_str = random.choice(list(accounts))
            account_info = json.loads(account_str)
            crawler_phone = account_info.get("phone")
            running_key = self.running_key.format(crawler_phone)
            is_running = await redis_client.get(running_key)
            if is_running:
                continue
            else:
                await redis_client.set(running_key, value=crawler_phone)
            # 获取今日的搜索次数
            request_count_today_key = self.request_count_today.format(crawler_phone)
            today_searches_count = await redis_client.get(request_count_today_key) or 0
            if not str(today_searches_count).isdigit():
                today_searches_count = 0
            if int(today_searches_count) >= self.max_daily_searche_number:
                await redis_client.delete(running_key)
                continue
            client = None
            # 开始进行任务
            task_phone = await redis_client.spop(self.task_key)
            ismember = await redis_client.sismember(self.succ_key, task_phone)
            if ismember:
                await redis_client.delete(running_key)
                continue
            try:
                # 开始登录账号
                proxy_key = self.proxy_key.format(crawler_phone)
                proxy = await self.get_proxy(proxy_key)
                client = await self.login(account_str, proxy)
                if client is None:
                    await redis_client.delete(running_key)
                    await redis_client.delete(proxy_key)
                    continue
                now_time = time.strftime("%F", time.localtime())
                logger.info(f"开始搜索{task_phone}的信息")
                result = await client(ImportContactsRequest([InputPhoneContact(random.randrange(-2 ** 63, 2 ** 63), task_phone, ' ', ' ')]))
                await redis_client.set(running_key, value=crawler_phone, ex=self.search_cd)
                retry_contacts = result.retry_contacts
                if retry_contacts:
                    logger.warning(result.to_dict())
                    logger.warning(retry_contacts)
                    phone_total = await redis_client.get(self.phone_total_key.format(crawler_phone))
                    logger.warning(f"【{crawler_phone}】添加好友过多，禁止添加，今日搜索次数【{today_searches_count}】,历史搜索次数【{phone_total}】")
                    await redis_client.sadd(self.task_key, task_phone)
                    await redis_client.srem(self.account_key, account_str)
                    await redis_client.sadd(self.banned_key.format(today), account_str)
                    await redis_client.delete(running_key)
                    continue
                else:
                    # 请求计数
                    if today_searches_count == 0:
                        await redis_client.set(request_count_today_key, value="1", ex=24 * 60 * 60)
                    else:
                        await redis_client.incr(request_count_today_key)
                    await redis_client.incr(self.phone_total_key.format(crawler_phone))
                    users = result.users
                    if users:
                        logger.info("用户手机号码:" + users[0].phone)
                        if users[0].username is not None:
                            logger.info("用户名称:" + users[0].username)
                        status = users[0].status
                        last_online_time = None
                        if isinstance(status, UserStatusOffline):
                            last_online_time = int(status.was_online.timestamp())
                            logger.info("用户最后在线时间" + status.was_online.strftime("%Y-%m-%d %R"))
                        elif isinstance(status, UserStatusOnline):
                            last_online_time = int(status.expires.timestamp())
                            logger.info("用户最后在线时间" + status.expires.strftime("%Y-%m-%d %R"))
                        photo = users[0].photo
                        user_id = users[0].id
                        user_avatar = None
                        if photo:
                            user_photo_byte = await client.download_profile_photo(entity=users[0], file=bytes, download_big=False)
                            oss_path = oss_base_path.format(f"/user/{now_time}/{user_id}.jpg")
                            upload_success = await upload_file_to_oss(oss_path, user_photo_byte)
                            if not upload_success:
                                await redis_client.sadd(error_key, f"手机号【{task_phone}】头像上传失败")
                            user_avatar = save_url.format(oss_path)
                        await asyncio.sleep(1)
                        full = await client(GetFullUserRequest(users[0]))
                        about = full.full_user.about
                        del_result = await client(DeleteContactsRequest([task_phone]))
                        del_user = del_result.users
                        if del_user[0].first_name is not None:
                            logger.info("用户first_name:" + del_user[0].first_name)
                        if del_user[0].last_name is not None:
                            logger.info("用户last_name:" + del_user[0].last_name)
                        user_info = {
                            "avatar": user_avatar,
                            "firstName": del_user[0].first_name,
                            "lastName": del_user[0].last_name,
                            "lastOnline": last_online_time,
                            "userName": users[0].username,
                            "userPhone": users[0].phone,
                            "userId": users[0].id,
                            "message": True,
                            "about": about,
                            "crawlerPhone": crawler_phone
                        }
                        await redis_client.sadd(self.succ_key, task_phone)
                        await redis_client.sadd(user_kafka_key, json.dumps({"Key": "user", "Body": user_info}, ensure_ascii=False))
                    else:
                        logger.warning(f"{task_phone},用户不存在")
                        user_info = {
                            "message": False,
                            "error": "用户不存在",
                            "userPhone": task_phone,
                            "crawlerPhone": crawler_phone
                        }
                logger.info(user_info)
                await redis_client.sadd(self.results_key, json.dumps(user_info, ensure_ascii=False))
            except (Exception,) as e:
                logger.error(e)
                if "Cannot find any entity corresponding to" not in str(e):
                    await redis_client.sadd(self.task_key, task_phone)
                else:
                    user_info = {
                        "message": False,
                        "error": "用户不存在",
                        "userPhone": task_phone,
                        "crawlerPhone": crawler_phone
                    }
                    logger.info(user_info)
                    await redis_client.sadd(self.results_key, json.dumps(user_info, ensure_ascii=False))
                if "A wait of " in str(e):
                    wait_time = re.search("\d+", str(e))
                    if wait_time:
                        wait_time_int = int(wait_time.group())
                        await redis_client.set(running_key, value=crawler_phone, ex=wait_time_int)
            finally:
                if client:
                    await client.disconnect()

    async def get_proxy(self, proxy_key, max_number=10):
        proxy_str = await redis_client.get(proxy_key)
        if proxy_str:
            proxy = json.loads(proxy_str)
            return proxy
        while max_number > 0:
            try:
                await asyncio.sleep(0.5)
                resp = httpx.get(self.port_url, headers={"Token": "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"})
                username = resp.json()["data"][0].split(":")[0]
                password = resp.json()["data"][0].split(":")[1]
                proxy = {
                    'proxy_type': 'http',
                    'addr': "gate1.ipweb.cc",
                    'port': 7778,
                    'username': username,
                    'password': password,
                    'rdns': True
                }
                await redis_client.set(proxy_key, json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
                return proxy
            except (Exception,) as e:
                max_number -= 1
                logger.error(e)
                await asyncio.sleep(1)

    async def start(self):
        while True:
            try:
                config_str = await redis_client.get(self.config_key)
                config = json.loads(config_str)
                self.max_daily_searche_number = config["max_daily_searche_number"]
                self.search_cd = config["search_cd"]
                task_number = await redis_client.scard(self.task_key)
                if task_number < 1:
                    break
                logger.info("剩余任务数{}".format(task_number))
                tasks = []
                tasks.extend([self.search_user_info_by_phone() for _ in range(8)])
                await asyncio.gather(*tasks)
            except (Exception,) as e:
                logger.error(e)
            finally:
                await asyncio.sleep(10)


if __name__ == '__main__':
    user_info = searchUserInfoByPhone()
    asyncio.run(user_info.start())
