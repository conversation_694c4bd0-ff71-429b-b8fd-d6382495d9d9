# -*- coding: utf-8 -*-
# @Time : 2023/10/30 9:58
# @Site :
# @File : tg_gadgets.py
# @Software: PyCharm
import re
import json
import asyncio
import aioredis
from loguru import logger
from telethon import TelegramClient
from telethon.sessions import StringSession
from utils.config import log_path, redis_host, redis_port, redis_password, error_key, api_id, api_hash
from utils.crawl_tools import get_machine_ip, upload_file_to_oss, get_history_msg, download_media_bytes


class TGgadgets(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.redis_client = aioredis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)
        # 上传失败的key
        self.upload_fail_key = "tg_spider:upload_fail"
        # 账号库存的key
        self.account_inventory_key = "tg_spider:temp_banned"
        # 被封禁账号的key
        self.account_banned_key = "tg_spider:account_banned"
        # 群监控被封禁账号的key
        self.monitor_banned_key = "tg_spider:monitor_banned"
        # 补传附件的key
        self.account_key = "tg_spider:account:{}"
        # 单个账号最大的采集次数
        self.max_requests_number = 100
        # 采集次数的key
        self.requests_num_key = "tg_spider:crawl:crawl_count:{}"

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        client = None
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"附件补充上传登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(self.account_banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            if client:
                await client.disconnect()
            if "two different IP" in str(e):
                await self.redis_client.sadd(self.account_banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
            error_mess = f"附件补充上传登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            await self.redis_client.sadd(error_key, error_mess)

    async def automatic_replace_account(self):
        """
        :return:
        """
        while True:
            try:
                logger.info("开始自动替换被封禁的账号")
                binding_account_info = await self.redis_client.smembers(self.account_banned_key)
                account_inventory_number = await self.redis_client.scard(self.account_inventory_key)
                if binding_account_info and account_inventory_number > 0:
                    for banned_info in binding_account_info:
                        banned_json = json.loads(banned_info)
                        binding_account_ip = banned_json["binding_account_ip"]
                        banned_phone = banned_json["banned_phone"]
                        key = f"tg_spider:account:{binding_account_ip}"
                        original_data_str = await self.redis_client.get(key)
                        original_data = eval(original_data_str)
                        register_data = await self.redis_client.spop(self.account_inventory_key)
                        original_data.append(json.loads(register_data))
                        await self.redis_client.set(key, json.dumps([account for account in original_data if account.get('phone') != banned_phone], ensure_ascii=False))
                        await self.redis_client.srem(self.account_banned_key, banned_info)
                else:
                    await asyncio.sleep(60 * 1)
            except (Exception,) as e:
                logger.error(e)

    async def automatic_replace_monitor_account(self):
        """
        :return:
        """
        while True:
            try:
                logger.info("开始自动替换群监控被封禁的账号")
                binding_account_info = await self.redis_client.smembers(self.monitor_banned_key)
                account_inventory_number = await self.redis_client.scard(self.account_inventory_key)
                if binding_account_info and account_inventory_number > 0:
                    for banned_info in binding_account_info:
                        banned_json = json.loads(banned_info)
                        binding_account_ip = banned_json["binding_account_ip"]
                        banned_phone = banned_json["banned_phone"]
                        key = f"tg_spider:monitor_account:{binding_account_ip}"
                        original_data_str = await self.redis_client.get(key)
                        original_data = json.loads(original_data_str)
                        supplement_data = await self.redis_client.spop(self.account_inventory_key)
                        original_data.append(json.loads(supplement_data))
                        # 获取封禁账号加入的群组
                        join_group_str = await self.redis_client.hget("tg_spider:group_join_info", banned_phone)
                        if join_group_str:
                            join_group = json.loads(join_group_str)
                            for group in join_group:
                                join_group_id = group.get('id')
                                join_group_link = group.get('link')
                                await self.redis_client.hset("tg_spider:politics_links", join_group_id, join_group_link)
                                await self.redis_client.srem("tg_spider:group_join_succ", join_group_id)
                        # 替换新账号
                        await self.redis_client.set(key, json.dumps([account for account in original_data if account.get('phone') != banned_phone], ensure_ascii=False))
                        # 删除封禁信息
                        await self.redis_client.srem(self.monitor_banned_key, banned_info)
                        # 删除封号的加群信息
                        await self.redis_client.hdel("tg_spider:group_join_info", banned_phone)
                else:
                    await asyncio.sleep(60 * 1)
            except (Exception,) as e:
                logger.exception(e)

    async def automatic_upload(self, account_info, machine_ip):
        logger.info("开始自动进行上传失败的附件上传")
        while True:
            try:
                phone = account_info.get("phone")
                client = await self.login(account_info, machine_ip)
                if not client:
                    break
                upload_fail_number = await self.redis_client.scard(self.upload_fail_key)
                if not upload_fail_number:
                    await asyncio.sleep(60 * 15)
                    continue
                upload_fail = await self.redis_client.srandmember(self.upload_fail_key)
                requests_num_key = self.requests_num_key.format(phone)
                number_of_requests = await self.redis_client.get(requests_num_key) or 0
                if not str(number_of_requests).isdigit():
                    number_of_requests = 0
                if int(number_of_requests) > self.max_requests_number:
                    await asyncio.sleep(60 * 60 * 2)
                    continue
                upload_fail_info = json.loads(upload_fail)
                channel = upload_fail_info.get('channel')
                offset = int(upload_fail_info.get('offset_id'))
                oss_path = upload_fail_info.get('oss_path')
                media_type = upload_fail_info.get('media_type')
                entity = await client.get_entity(channel)
                logger.info(f"开始补充上传 {channel} 偏移 {offset} 的附件")
                storage = await self.redis_client.sismember("tg_spider:storage_group", entity.id)
                if not oss_path or not media_type:
                    await get_history_msg(client, 1, channel, offset, True, entity)
                    await self.redis_client.srem(self.upload_fail_key, upload_fail)
                else:
                    message = await client.get_messages(entity=entity, ids=offset)
                    if message:
                        if media_type in ["video", "emoji", "photo"]:
                            if media_type == "video" and storage:
                                media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
                            else:
                                media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type, thumb=True)
                        else:
                            media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
                        logger.info(f"开始上传 {channel} 偏移 {offset} 的附件")
                        upload_success = await upload_file_to_oss(oss_path, media_bytes, headers={'x-oss-expires': '94608000'})
                        if upload_success:
                            await self.redis_client.srem(self.upload_fail_key, upload_fail)
                if number_of_requests == 0:
                    await self.redis_client.set(requests_num_key, value="1", ex=24 * 60 * 60)
                else:
                    ttl = await self.redis_client.ttl(requests_num_key)
                    if ttl == -1:
                        await self.redis_client.expire(requests_num_key, 24 * 60 * 60)
                    await self.redis_client.incr(requests_num_key)
                logger.info(f"补充上传 {channel} 偏移 {offset} 的附件执行完成")
            except (Exception,) as e:
                logger.exception(e)
                if "A wait of " in str(e):
                    wait_time = re.search("\d+", str(e))
                    if wait_time:
                        wait_time_int = int(wait_time.group())
                        await asyncio.sleep(wait_time_int)
            finally:
                await asyncio.sleep(3)

    async def start_crawler(self):
        while True:
            try:
                machine_ip = get_machine_ip()
                account_key = self.account_key.format(machine_ip)
                account_list = await self.redis_client.get(account_key) or "[]"
                accounts = json.loads(account_list)
                if len(accounts) == 0:
                    logger.warning(f"【{machine_ip}】下没有绑定的账号")
                tasks = []
                # for account_info in accounts:
                #     phone = account_info.get("phone")
                #     requests_num_key = self.requests_num_key.format(phone)
                #     number_of_requests = await self.redis_client.get(requests_num_key) or 0
                #     if int(number_of_requests) < self.max_requests_number:
                #         tasks.append(self.automatic_upload(account_info, machine_ip))
                #     else:
                #         logger.warning(f"【{phone}】今日请求次数已经消耗完了,共请求了【{number_of_requests}】次")
                tasks.append(self.automatic_replace_account())
                # tasks.append(self.automatic_replace_monitor_account())
                await asyncio.gather(*tasks)
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 10)


if __name__ == '__main__':
    tg_gadgets = TGgadgets()
    asyncio.run(tg_gadgets.start_crawler())
