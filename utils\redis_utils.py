# -*- coding: utf-8 -*-
import os
import json
import redis
import datetime
from math import ceil
from pathlib import Path
from operator import itemgetter
from config import redis_host, redis_port, redis_password


def scan_get_all_key(match, count=10000):
    key_names = []
    cursor = 0
    while True:
        cursor, keys = redis_client.scan(cursor=cursor, match=match, count=count)
        for key in keys:
            key_names.append(key)
        if cursor == 0:
            break
    return key_names


def crawl_counts_statistic():
    """
    获取账号请求次数
    :return:
    """
    requests_number_keys = scan_get_all_key("tg_spider:crawl:crawl_count:*")
    requests_numbers = []
    for _ in requests_number_keys:
        phone = _.replace("tg_spider:crawl:crawl_count:", "")
        requests_number = redis_client.get(_) or 0
        ex = redis_client.ttl(_)
        requests_numbers.append({"phone": phone, "number": requests_number, "ex": ex})
        if ex == -1:
            redis_client.expire(_, 80000)
    return requests_numbers


def get_all_account_info() -> dict:
    """
    获取所有的账号和机器ip的绑定信息
    :return:
    """
    account_dict = {}
    account_keys = scan_get_all_key("tg_spider:account:*")
    for _ in account_keys:
        account_info = json.loads(redis_client.get(_))
        ip = _.replace("tg_spider:account:", "")
        new_account_info = []
        for account in account_info:
            if account_dict.get(account["phone"]):
                print(f"有账号重复登录 {account}, 机器ip: {ip},另外一台ip是 {account_dict[account['phone']]}")
                continue
            account_dict[account["phone"]] = ip
            new_account_info.append(account)
        redis_client.set(_, json.dumps(new_account_info, ensure_ascii=False))
    return account_dict


def crawler_statistics():
    """检查机器有没有进行抓取任务"""
    today = datetime.datetime.now()
    year = today.year
    month = f"{today.month:02d}"
    day = f"{today.day:02d}"
    # 获取到所有的账号绑定信息
    account_dict = get_all_account_info()
    all_accounts = list(account_dict.keys())
    # 获取当前所有账号的抓取次数
    current_crawl_counts = crawl_counts_statistic()
    print(f"共有【{len(all_accounts)}】个账号已经登录了【{len(current_crawl_counts)}】个账号")
    print("*" * 50)
    crawler_slow = []
    # 对当前抓取的次数进行判断,如果次数少于100并且已经抓取了一段时间,就是没有任务
    for _ in current_crawl_counts:
        phone = _["phone"]
        number = _["number"]
        ex = _["ex"]
        # 一个账号2个小时都没采集完100次,视为缓慢
        if int(number) < 100 and (86400 - int(ex)) > 60 * 60 * 2:
            ip = account_dict.get(phone) or ""
            _["ip"] = ip
            crawler_slow.append(_)
    crawler_slow.sort(key=itemgetter("ip", 'number', 'ex'))
    print(f"共有 【{len(crawler_slow)}】个账号采集较慢,详情如下:")
    [print(f"采集任务卡顿,【{_['phone']}】请求次数是【{_['number']}】,请求耗时是【{round((86400 - _['ex']) / 60 / 60, 2)}】时,请用【 ssh admin@{_['ip']} 】查看") for _ in crawler_slow]
    print("*" * 50)
    # 如果一台机器所有账号都没有登录,肯定是没有抓取的
    account_keys = scan_get_all_key("tg_spider:account:*")
    for _ in account_keys:
        account_info = json.loads(redis_client.get(_))
        ip = _.replace("tg_spider:account:", "")
        not_crawl = True
        for account in account_info:
            requests_number_key = "tg_spider:crawl:crawl_count:{}".format(account["phone"])
            requests_number = redis_client.get(requests_number_key)
            if requests_number:
                not_crawl = False
                break
        if not_crawl:
            print(f"所有的账号都没有登录, 请用【 ssh admin@{ip} 】查看,可能需要使用【 pkill -f group_msg_crawl 】")
    print(f"更新采集日志查看使用【 tail -f /andata/server/crawler/logs/GroupMsgCrawl_{year}_{month}_{day}.log 】")
    print("重启程序使用【 pkill -f group_msg_crawl 】")
    print("*" * 50)

    monitor_keys = scan_get_all_key("tg_spider:monitor_account:*")
    monitor_keys.sort()
    for key in monitor_keys:
        ip = key.replace("tg_spider:monitor_account:", "")
        print(f"实时采集机器,请用【 ssh admin@{ip} 】查看")
    print(f"实时采集日志查看使用【 tail -f /andata/server/crawler/logs/GroupMonitor_{year}_{month}_{day}.log 】")
    print(f"实时采集日志查看使用【 tail -f /andata/server/crawler/logs/GroupMonitorTools_{year}_{month}_{day}.log 】")
    print(f"去除重复群组使用【 python3 /andata/server/crawler/tg_message_crawler/repeat_group.py 】")
    print("实时采集重启程序使用【 pkill -f group_monitor 】")


def get_all_monitor_account():
    all_phones = []
    monitor_ips = scan_get_all_key("tg_spider:monitor_account:*")
    print(f"共有【{len(monitor_ips)}】台服务器")
    for _ in monitor_ips:
        ip = _.replace("tg_spider:monitor_account:", "")
        account = redis_client.get(_)
        accounts_info = json.loads(account)
        phones = [_["phone"] for _ in accounts_info]
        all_phones.extend(phones)
        print(f"【{ip}】下有以下账号【{phones}】")
    print("****************")
    print(f"共有【{len(all_phones)}】个账号")
    return all_phones


def add_account(account_max_number=10):
    new_machine_ip = [
        "*************",
        "*************",
        "*************",
        "*************",
        "*************",
        "*************",
        "*************",
        "*************",
        "*************",
        "*************"
    ]
    # account_keys = scan_get_all_key("tg_spider:account:*")
    account_keys = [f"tg_spider:account:{_}" for _ in new_machine_ip]
    register_key = "tg_spider:temp_banned:2024-07-02"
    for key in account_keys:
        # 先获取到原始的账号信息
        currentl_account_data = redis_client.get(key)
        if currentl_account_data:
            currentl_account_data = eval(currentl_account_data)
        else:
            currentl_account_data = []
        currentl_account_number = len(currentl_account_data)
        print(f"{key}:{currentl_account_number}")
        gap = account_max_number - len(currentl_account_data)
        if gap < 1:
            new_account_data = currentl_account_data[0:account_max_number]
            superfluous_accout = currentl_account_data[account_max_number:]
            redis_client.set(key, json.dumps(new_account_data, ensure_ascii=False))
            redis_client.sadd(register_key, *[json.dumps(_, ensure_ascii=False) for _ in superfluous_accout])
        else:
            register_balance = redis_client.scard(register_key) or 0
            if register_balance == 0:
                break
            if register_balance < gap:
                gap = register_balance
            register_datas = redis_client.spop(register_key, gap)
            datas = [json.loads(_) for _ in register_datas]
            datas.extend(currentl_account_data)
            redis_client.set(key, json.dumps(datas, ensure_ascii=False))
            print(f"将 {gap} 个账号填充到了 {key} 中")


def sub_account():
    account = eval(redis_client.get("tg_spider:account:*************"))
    retention_account = account[0:4]
    redis_client.set("tg_spider:monitor_account:*************", json.dumps(account[4:], ensure_ascii=False))
    redis_client.set("tg_spider:account:*************", json.dumps(retention_account, ensure_ascii=False))


def del_key():
    obj_redis_client = redis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30, db=1)
    cursor = 0
    while True:
        cursor, keys = obj_redis_client.scan(cursor=cursor, match="tg_spider:media_id:*", count=100000)
        with obj_redis_client.pipeline() as pipe:
            for key in keys:
                pipe.delete(key)
            pipe.execute()
        if cursor == 0:
            break


def task_balance(key="tg_spider:phone_search:task", task_name="手机号搜索"):
    number = redis_client.scard(key)
    print(f"【{task_name}】剩余任务数为:【{number}】")


def add_promotion_account(account_number=100):
    account_key = "tg_spider:promotion:account"
    account_pool = "tg_spider:temp_banned:2024-03-29"
    number_of_current_accounts = redis_client.scard(account_key)
    print(f"推广账号数量【{number_of_current_accounts}】")
    if number_of_current_accounts < account_number:
        accounts = redis_client.srandmember(account_pool, account_number - number_of_current_accounts)
        for _ in accounts:
            redis_client.sadd(account_key, _)
        promotion_accounts = redis_client.smembers(account_key)
        for _ in promotion_accounts:
            redis_client.srem(account_pool, _)


def add_special_account(account_number=15):
    account_key = "tg_spider:promotion:account"
    account_pool = "tg_spider:temp_banned:2024-03-29"
    special_key = "tg_spider:promotion:special_phone"
    number_of_current_accounts = redis_client.scard(special_key)
    print(f"特殊账号数量【{number_of_current_accounts}】")
    if number_of_current_accounts < account_number:
        accounts = redis_client.srandmember(account_pool, account_number - number_of_current_accounts)
        for _ in accounts:
            redis_client.sadd(account_key, _)
            redis_client.srem(account_pool, _)
            obj = json.loads(_)
            redis_client.sadd("tg_spider:promotion:special_phone", obj["phone"])


def add_phone_search_account(account_number=15000):
    account_key = "tg_spider:phone_search:account"
    account_pools = [
        "tg_spider:temp_banned:2024-09-08",
    ]
    for account_pool in account_pools:
        number_of_current_accounts = redis_client.scard(account_key)
        print(f"手机号搜索账号数量【{number_of_current_accounts}】")
        if number_of_current_accounts < account_number:
            accounts = redis_client.srandmember(account_pool, account_number - number_of_current_accounts)
            for _ in accounts:
                redis_client.sadd(account_key, _)
            promotion_accounts = redis_client.smembers(account_key)
            for _ in promotion_accounts:
                redis_client.srem(account_pool, _)


def merge_miss_data_task():
    missing_data_infos = redis_client.smembers("tg_spider:crawl:missing_data_info")
    for missing_data_info in missing_data_infos:
        _ = json.loads(missing_data_info)
        link = _["link"]
        min_id = _["min_id"]
        max_id = _["max_id"]
        back_info_str = redis_client.hget("tg_spider:crawl:merge_missing_data_info", link)
        info = {"min_id": min_id, "max_id": max_id}
        if back_info_str:
            back_info = json.loads(back_info_str)
            back_info_min = back_info.get("min_id")
            back_info_max = back_info.get("max_id")
            if int(back_info_min) < int(min_id):
                info["min_id"] = back_info_min
            if int(back_info_max) > int(max_id):
                info["max_id"] = back_info_max
        redis_client.hset("tg_spider:crawl:merge_missing_data_info", link, json.dumps(info, ensure_ascii=False))


def refresh_miss_data_task():
    redis_client.delete("tg_spider:crawl:missing_data_info")
    keys = redis_client.hkeys("tg_spider:crawl:merge_missing_data_info")
    for _ in keys:
        info = redis_client.hget("tg_spider:crawl:merge_missing_data_info", _)
        info_json = json.loads(info)
        info_json["link"] = _
        redis_client.sadd("tg_spider:crawl:missing_data_info", json.dumps(info_json, ensure_ascii=False))
    redis_client.delete("tg_spider:crawl:merge_missing_data_info")


def list_slicing(data_list: list, num=100):
    """
    :param data_list: 需要切割的list
    :param num: 需要多少元素一个list,默认100个一个list
    :return:
    """
    new_list = []
    for index in range(ceil(len(data_list) / num)):
        new_list.append(data_list[index * num:(index + 1) * num])
    return new_list


def load_link_files():
    file_path = str(Path(__file__).parent.parent) + 'file/link.txt'
    if os.path.exists(file_path):
        with open(file_path, encoding="utf-8") as file:
            for link in file.readlines():
                redis_client.sadd("tg_spider:crawl:history:task_links", link.strip())


def load_account_files():
    account_key = "tg_spider:phone_search:account"
    file_path = str(Path(__file__).parent.parent) + '/file/tg_account.txt'
    if os.path.exists(file_path):
        print("开始加载账号文件")
        with open(file_path, encoding="utf-8") as file:
            accounts = json.loads(file.read())
            redis_client.sadd(account_key, *[json.dumps(_, ensure_ascii=False) for _ in accounts])
        os.remove(file_path)
    number_of_current_accounts = redis_client.scard(account_key)
    print(f"账号数量:【{number_of_current_accounts}】")


def load_phone_search():
    file_path = str(Path(__file__).parent.parent) + '/file/phone_task.txt'
    if os.path.exists(file_path):
        with open(file_path, encoding="utf-8") as file:
            accounts = file.read().split('\n')
            redis_client.sadd("tg_spider:phone_search:task", *accounts)


def reset_group_join_succ():
    values = redis_client.hvals("tg_spider:group_join_info")
    ids = []
    not_group_id = []
    for v in values:
        for _ in json.loads(v):
            if isinstance(_, dict):
                _id = _["id"]
                if str(_id).isdigit() is False:
                    not_group_id.append(_id)
                ids.append(_.get("id"))
            else:
                not_group_id.append(_)
    redis_client.sadd("tg_spider:not_group_id", *not_group_id)
    with open("not_group_id.txt", "w") as file:
        file.write(json.dumps(not_group_id))


def temp():
    today_join_group_count_keys = scan_get_all_key("tg_spider:today_join_group_count:*")
    for today_join_group_count_key in today_join_group_count_keys:
        t = redis_client.ttl(today_join_group_count_key)
        if t == -1:
            redis_client.delete(today_join_group_count_key)


def del_():
    linke = redis_client.smembers("tg_spider:group_join_succ")
    private = [_ for _ in linke if isinstance(_, str) and _.isdigit() is False]
    redis_client.srem("tg_spider:group_join_succ", *private)
    for _ in private:
        redis_client.hset("tg_spider:politics_links", _, _)


if __name__ == '__main__':
    redis_client = redis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)
    task_balance()
    task_balance("tg_spider:crawl:history:task_links", "历史消息采集")
    crawler_statistics()
    load_account_files()
    # add_phone_search_account()
    # add_account()
    # del_()
    # temp()
    # reset_group_join_succ()
    # load_link_files()
    # supplementary_account()
    # account_return()
    # temp_running2()
    # load_phone_search()
    # temp_running()
    # increase_special_account(50)
    # increase_phone_search_account(200000)
    # temp_running2()
