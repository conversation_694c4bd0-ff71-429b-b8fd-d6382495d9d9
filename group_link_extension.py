# -*- coding: utf-8 -*-
import re
import json
import asyncio
import aioredis
from loguru import logger
from telethon import TelegramClient, events
from telethon.sessions import StringSession
from utils.crawl_tools import get_machine_ip, parse_message
from telethon.tl.functions.channels import Join<PERSON><PERSON><PERSON>Request
from utils.config import error_key, api_id, api_hash, redis_host, redis_port, redis_password, msg_kafka_key, log_path, failure_key


class GroupLinkExtension(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.redis_client = aioredis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)
        self.account_key = "tg_spider:monitor_account:{}"
        self.task_key = "tg_spider:search_group_link_wait"
        self.group_join_info_key = "tg_spider:group_join_info"
        self.join_max = 350
        self.join_cd = 60

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群链接扩展账号登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            error_mess = f"群链接扩展账号登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            logger.error(error_mess)
            await self.redis_client.sadd(error_key, error_mess)

    async def join_group(self, account_info, machine_ip):
        phone = account_info.get("phone")
        client = await self.login(account_info, machine_ip)
        group_join_str = await self.redis_client.hget(self.group_join_info_key, phone) or "[]"
        group_join_info = json.loads(group_join_str)
        join_group_number = len(group_join_info)
        while join_group_number < self.join_max:
            task_balance = await self.redis_client.scard(self.task_key)
            if not task_balance:
                break
            group_link_str = await self.redis_client.spop(self.task_key)
            group_link_info = json.loads(group_link_str)
            group_link = group_link_info.get("link")
            try:
                logger.info(f"用户【{phone}】开始加入【{group_link}】")
                await client(JoinChannelRequest(group_link))
                logger.info(f"用户【{phone}】加入【{group_link}】成功")
                group_join_info.append(group_link)
                await self.redis_client.hset(self.group_join_info_key, phone, json.dumps(group_join_info, ensure_ascii=False))
                await asyncio.sleep(self.join_cd)
            except (Exception,) as e:
                if "No user has" in str(e) or "Nobody is using this username" in str(e):
                    logger.error(f"【{group_link}】群不存在")
                    await self.redis_client.sadd(failure_key, group_link)
                elif "The chat the user tried to join has expired" in str(e):
                    logger.error(f"【{group_link}】用户尝试加入的聊天已过期，不再有效")
                    await self.redis_client.sadd(failure_key, group_link)
                elif "A wait of " in str(e):
                    await self.redis_client.sadd(self.task_key, group_link_str)
                    wait_time = re.search("\d+", str(e))
                    if wait_time:
                        wait_time_int = int(wait_time.group())
                        await asyncio.sleep(wait_time_int)
                elif "You have successfully requested to join this chat or channel" in str(e):
                    continue
                else:
                    await self.redis_client.sadd(self.task_key, group_link_str)
                logger.error(e)
            finally:
                join_group_number = len(group_join_info)

    async def monitor(self, account_info, machine_ip):
        """
        获取群的实时消息
        :return:
        """
        while True:
            phone = account_info.get("phone")
            client = await self.login(account_info, machine_ip)
            logger.info(f"{phone} 开始监听新消息")

            @client.on(events.NewMessage())
            async def my_event_handler(event):
                try:
                    if event.is_private is False:
                        channel_id = event.chat.id
                        channel = await self.redis_client.hget("tg_spider:group_info", channel_id)
                        channel_name = event.chat.title
                        broadcast = event.chat.broadcast
                        if broadcast:
                            category = "broadcast"
                        else:
                            category = "group"
                        message_dict = await parse_message(client, event.message, channel_id, channel, channel_name)
                        message_dict["category"] = category
                        ext_links = message_dict.get("extLinks")
                        await self.redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": message_dict}, ensure_ascii=False))
                        if ext_links:
                            links = list(set([self.tg_link_extract(link) for link in ext_links]))
                            logger.info(links)
                            await self.redis_client.sadd("tg_spider:identification:task", *links)
                except (Exception,) as e:
                    logger.exception(e)

            await client.connect()
            await client.run_until_disconnected()

    @staticmethod
    def tg_link_extract(link):
        rule_one = re.search("https://t\.me/[^/?]{1,100}", link)
        if rule_one:
            return rule_one.group().replace("@", "").rstrip("https:")

    async def start(self):
        machine_ip = get_machine_ip()
        account_key = self.account_key.format(machine_ip)
        account_list = await self.redis_client.get(account_key) or "[]"
        account_infos = json.loads(account_list)
        tasks = [self.join_group(account_info, machine_ip) for account_info in account_infos]
        tasks.extend([self.monitor(account_info, machine_ip) for account_info in account_infos])
        await asyncio.gather(*tasks)


if __name__ == '__main__':
    group_link_extension = GroupLinkExtension()
    asyncio.run(group_link_extension.start())
