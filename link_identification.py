# -*- coding: utf-8 -*-
# @Time : 2023/8/31 13:54
# @Site : 
# @File : link_identification.py
# @Software: PyCharm
import time
import httpx
import redis
from loguru import logger
from scrapy.selector import Selector
from utils.crawl_tools import tg_link_extract
from utils.config import redis_port, redis_host, redis_password, log_path


class Link_Identification(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )

        self.headers = {
            "authority": "t.me",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "sec-ch-ua": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
            "upgrade-insecure-requests": "1",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.task_key = "tg_spider:identification:task"
        self.succ_key = "tg_spider:identification:success"
        redis_pool = redis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, health_check_interval=30)
        self.redis_client = redis.Redis(connection_pool=redis_pool)

    def send_requests(self, url, model="get", max_num=5, **kwargs):
        requests_model = {"post": httpx.post, "get": httpx.get}[model.lower()]
        while max_num > 0:
            try:
                time.sleep(0.5)
                resp = requests_model(url, headers=self.headers, verify=False, **kwargs, timeout=10)
                if resp.status_code == 200:
                    return resp
                else:
                    max_num -= 1
            except Exception as e:
                logger.error(e)

    def identification(self, url):
        try:
            resp = self.send_requests(url)
            html = Selector(text=resp.text)
            page_extra = "".join(html.xpath('//div[@class="tgme_page_extra"]//text()').getall())
            if page_extra:
                if "members" in page_extra:
                    category = "group"
                elif "subscriber" in page_extra:
                    category = "channel"
                else:
                    category = "user"
            else:
                category = ""
            logger.info(f"【{url}】的类型是：{category}")
            if category:
                if category in ["group"]:
                    self.redis_client.sadd("tg_spider:crawl:history:task_links", url)
                else:
                    self.redis_client.sadd(f"tg_spider:link:{category}", url)
                    self.redis_client.sadd(self.succ_key, url)
            else:
                self.redis_client.sadd("tg_spider:identification:not_user", url)
        except (Exception,) as e:
            self.redis_client.sadd("tg_spider:identification:fali_task", url)
            logger.error(e)

    def crawl(self):
        while True:
            try:
                task_number = self.redis_client.scard(self.task_key)
                if task_number < 1:
                    time.sleep(20)
                    continue
                url = tg_link_extract(self.redis_client.spop(self.task_key))
                ismember = self.redis_client.sismember("tg_spider:identification:not_user", url) and self.redis_client.sismember(self.succ_key, url) or self.redis_client.sismember("tg_spider:crawl:history:crawl_success", url) or self.redis_client.sismember("tg_spider:crawl:history:task_links", url) or self.redis_client.sismember("tg_spider:link:channel", url) or self.redis_client.sismember("tg_spider:link:group", url) or self.redis_client.sismember("tg_spider:link:user", url) or self.redis_client.sismember("tg_spider:links_no_user", url)
                if ismember or url.count("-") > 3:
                    continue
                self.identification(url)
            except (Exception,) as e:
                print(e)


if __name__ == '__main__':
    link_identification = Link_Identification()
    link_identification.crawl()
