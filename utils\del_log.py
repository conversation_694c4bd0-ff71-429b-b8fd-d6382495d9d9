# -*- coding: utf-8 -*-
import os
import time
import os.path
import datetime
import schedule
from config import log_path

n_days_ago = 2


def delete_ndays_ago_log():
    for parent, dirnames, filenames in os.walk(log_path):
        for filename in filenames:
            full_name = parent + "/" + filename  # 文件全称
            create_time = int(os.path.getctime(full_name))  # 文件创建时间
            # 当前时间的n天前的时间
            delta_days = (datetime.datetime.now() - datetime.timedelta(days=n_days_ago))
            time_stamp = int(time.mktime(delta_days.timetuple()))
            if create_time < time_stamp:  # 创建时间在n天前的文件删除
                os.remove(os.path.join(parent, filename))


def stat_logfile():
    kill_command = "pkill -f group_msg_crawl"
    today = datetime.datetime.now()
    check_timestamp = today.timestamp()
    year = today.year
    month = f"{today.month:02d}"
    day = f"{today.day:02d}"
    logfile_path = f'/andata/server/crawler/logs/GroupMsgCrawl_{year}_{month}_{day}.log'
    if os.path.exists(logfile_path):
        statinfo = os.stat(logfile_path)
        last_change_time = statinfo.st_mtime
        if int(check_timestamp - last_change_time) > 1800:
            print("超过半小时没采集,重启采集")
            os.system(kill_command)
    else:
        print("日志文件不存在,重启采集")
        os.system(kill_command)


if __name__ == '__main__':
    delete_ndays_ago_log()
    stat_logfile()
    schedule.every().day.at("10:30").do(delete_ndays_ago_log)
    schedule.every().hour.do(stat_logfile)
    while True:
        schedule.run_pending()
        time.sleep(1)
