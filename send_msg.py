# -*- coding: utf-8 -*-
# @Time : 2023/11/22 14:18
# @Site : 
# @File : send_msg.py
# @Software: PyCharm
import re
import json
import asyncio
import aioredis
import httpx
import python_socks
from loguru import logger
from telethon import Telegram<PERSON>lient
from telethon.sessions import StringSession
from telethon.tl.functions.channels import Join<PERSON>hannelRequest
from telethon.tl.types import MessageActionChatAddUser

from utils.config import log_path, redis_host, redis_port, redis_password, api_id, api_hash, error_key, failure_key


class SendMessage(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # 待发送私人广告的任务key
        self.private_task_key = "tg_spider:send_private_info"
        # 待发送群广告的任务key
        self.group_task_key = "tg_spider:send_group_info"
        # 需要验证的key
        self.group_join_verification_key = "tg_spider:group_info_verification"
        # 广告发送成功的key
        self.succ_key = "tg_spider:send_succ"
        # 账号key
        self.account_key = "tg_spider:account:{}"
        # 广告发送成功计数
        self.send_num_key = "tg_spider:send_num:{}"

        self.send_cd = 30
        self.send_max = 100
        self.redis_client = aioredis.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)

    async def login(self, account_info, proxy):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{proxy}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {proxy}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"消息发送账号登录失败,服务器地址【{proxy}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except:
            error_mess = f"消息发送账号登录失败,服务器地址【{proxy}】,账号【{phone}】"
            logger.error(error_mess)
            await self.redis_client.sadd(error_key, error_mess)

    async def send_private_message(self, account_info, proxy):
        """
        发送私信
        :param account_info:
        :param proxy:
        :return:
        """
        phone = account_info.get("phone")
        client = await self.login(account_info, proxy)
        send_num_key = self.send_num_key.format(phone)
        send_number = await self.redis_client.get(send_num_key) or 0
        while int(send_number) < self.send_max:
            send_objectives_str = await self.redis_client.spop(self.private_task_key)
            if send_objectives_str:
                send_objectives_info = json.loads(send_objectives_str)
                user_id = send_objectives_info["user_id"]
                user_name = send_objectives_info["user_name"]
                user_link = f"https://t.me/{user_name}"
                ismember = await self.redis_client.sismember(self.succ_key, user_id)
                if ismember:
                    continue
                try:
                    entity = await client.get_entity(user_link)
                    message = f'Hi, {user_name}!'
                    await client.send_message(entity, message)
                    if send_number == 0:
                        await self.redis_client.set(send_num_key, value="1", ex=24 * 60 * 60)
                    else:
                        ttl = await self.redis_client.ttl(send_num_key)
                        if ttl == -1:
                            await self.redis_client.expire(send_num_key, 24 * 60 * 60)
                        await self.redis_client.incr(send_num_key)
                    send_number = await self.redis_client.get(send_num_key) or 0
                    logger.info(f"当前已经发送了【{send_number}】次,消息为{message}")
                    await self.redis_client.sadd(self.succ_key, user_id)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e):
                        logger.error(f"用户【{user_link}】不存在")
                        await self.redis_client.sadd(failure_key, user_link)
                    elif "A wait of " in str(e):
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            await asyncio.sleep(wait_time_int)
                finally:
                    await asyncio.sleep(self.send_cd)

    async def send_public_messages(self, account_info, proxy):
        """
        在群组里面发送信息
        :param account_info:
        :param proxy:
        :return:
        """
        phone = account_info.get("phone")
        client = await self.login(account_info, proxy)
        send_num_key = self.send_num_key.format(phone)
        send_number = await self.redis_client.get(send_num_key) or 0
        while int(send_number) < self.send_max:
            group_link = await self.redis_client.spop(self.group_task_key)
            print(group_link)
            if group_link:
                join_succ = True
                verification_required = await self.redis_client.sismember(self.group_join_verification_key, group_link)
                failure = await self.redis_client.sismember("tg_spider:links_no_user", group_link)
                if verification_required or failure:
                    continue
                try:
                    print("开始加群")
                    entity = await client.get_entity(group_link)
                    if entity.default_banned_rights.send_messages:
                        print("group_disable_sending_messages")
                        continue
                    if entity.broadcast:
                        print("is_broadcast")
                        continue
                    else:
                        print("is_group")
                    try:
                        join_status = await client(JoinChannelRequest(entity))
                        updates = join_status.updates
                        print(updates)
                    except (Exception,) as e:
                        if "You have successfully requested to join this chat or channel" in str(e):
                            updates = None
                            join_succ = True
                        elif "A wait of " in str(e):
                            await self.redis_client.sadd(self.group_task_key, group_link)
                            wait_time = re.search("\d+", str(e))
                            if wait_time:
                                wait_time_int = int(wait_time.group())
                                await asyncio.sleep(wait_time_int)
                            continue
                        else:
                            print(e)
                            continue
                    # 判断是否加群成功
                    if updates:
                        message = updates[-1]
                        action = message.message.action
                        message_id = message.message.id
                        if isinstance(action, MessageActionChatAddUser):
                            # 判断加群后最新的3条消息,看看是否有弹出来验证码
                            for i in range(1, 4):
                                print(f"获取最新的消息,第{i}次")
                                max_number = 2
                                while max_number > 0:
                                    verification_message = await client.get_messages(entity, ids=message_id + i)
                                    if verification_message:
                                        content = verification_message.message
                                        if content.count("验证") or content.count("限制") or content.count("封禁") or content.count("verification"):
                                            join_succ = False
                                            break
                                    else:
                                        max_number -= 1
                                        await asyncio.sleep(0.5)
                                        continue
                    # 如果成功加入,并且没有验证码
                    if join_succ:
                        logger.info(f"用户【{phone}】加入【{group_link}】成功,群名字是【{entity.title}】")
                        message = f'开启全新聊天体验，下载中文版聊天App，畅所欲言！无广告，高安全保障！超多贴心功能，轻松聊天不受限！加入Telegram中文版，与好友随时随地畅享畅聊时光。立即下载，畅享沟通快乐！https://www.tsoul.info/'
                        await client.send_message(entity, message)
                        if send_number == 0:
                            await self.redis_client.set(send_num_key, value="1", ex=24 * 60 * 60)
                        else:
                            ttl = await self.redis_client.ttl(send_num_key)
                            if ttl == -1:
                                await self.redis_client.expire(send_num_key, 24 * 60 * 60)
                            await self.redis_client.incr(send_num_key)
                        send_number = await self.redis_client.get(send_num_key) or 0
                        logger.info(f"当前已经发送了【{send_number}】次,消息为{message}")
                        await self.redis_client.sadd(self.succ_key, group_link)
                    else:
                        # 加入到加群需要验证的key,后面手动加群
                        await self.redis_client.sadd(self.group_join_verification_key, group_link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e):
                        logger.error(f"用户【{group_link}】不存在")
                        await self.redis_client.sadd(failure_key, group_link)
                    elif "A wait of " in str(e):
                        await self.redis_client.sadd(self.group_task_key, group_link)
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            await asyncio.sleep(wait_time_int)
                finally:
                    await asyncio.sleep(self.send_cd)

    async def contact_ban(self, account_info, proxy):
        phone = account_info.get("phone")
        logger.info(f"开始解封{phone}")
        client = await self.login(account_info, proxy)
        user_link = "https://t.me/SpamBot"
        entity = await client.get_entity(user_link)
        async with client.conversation(entity, timeout=10) as conv:
            await conv.send_message("/start")
            while True:
                response = await conv.get_response()
                messages = response.message
                print(messages)
                if messages.startswith("Unfortunately, some phone numbers may trigger a harsh response from our anti-spam systems."):
                    buttons = response.reply_markup.rows[0].buttons[0]
                    if buttons.text.count("complaint"):
                        await response.click(text=buttons.text)
                        continue
                elif messages.count("First, please confirm that you will never send this to strangers:"):
                    buttons = response.reply_markup.rows[0].buttons[0]
                    if buttons.text.count("No, I’ll never do any of this!"):
                        await response.click(text=buttons.text)
                        continue
                elif messages.startswith("Great! I’m very sorry if your account was limited by mistake. Please write me some details about your case,"):
                    await conv.send_message("Dear Telegram Support,My Telegram account has been restricted suddenly. I have several Telegram accounts, and I am doing the same activities with all these accounts. It is strange that this particular account of mine is in trouble. Please examine the issue and fix my problem. I request you to remove the spam from my account. Thanks in advance!")
                elif messages.startswith("Thank you! Your complaint has been successfully submitted."):
                    break
                elif messages.count("You've already submitted a complaint recently."):
                    break
                elif messages.count("Good news, no limits are currently applied to your account"):
                    break
            print("cancel chat")
            await conv.cancel_all()
        await client.disconnect()

    async def start_crawler(self):
        while True:
            try:
                accounts = [
                    {"phone": "***********", "account_key": "1AZWarzYBuyFdAC6ZKBkNFeAEes6-URXsqwd_7Mqyxj8YhRiaTIMzvhrbiBJFVG3exx93aZkCFMKhbelBsoUqZ4YmCidrfJWw85I6INxkIV1W1qEeSARycnme4MDhlv_pJQ5_2klO8gB-di3duNIkCSlld2x7RMJtjsYpP48rhfQyZNxuZ7Q9Ed3DlNmguCKK9UgL7A1rTF2XF9FliQP6wJG0myd_CA8nxxMyQHjoFDNZ3litEXzxLNocIo1YsrL3H10hmabVDaUL8CuK8N4DMi6kHRPQiNTAtwa1IdSNmuYw8zS61oG-3bg9Uji9Hg70k06XKsy-IqPO04mu-fps59kE49xIGAg="},
                    # {"phone": "************", "account_key": "1BJWap1wBuwQ4LNuYONQtn3Xo5ov8RzS5M87jiZBx_44Z0f3k-VYttV4Z3gaqu01Wxj2CAC5GHP7AgGefMN7jZdz2b5t8FwqLmL27OEXRpUef821BBPtTsvqBz5P-eMuFaifsIxBT6j4UfxeLFaA8XajBEfmsFpo5srKOu4XuHh7ZDzj4m8JLDnkpns8qQ0qYrC7IApo-3JAZrBgGVX_1E6x0ZAKlKn0J1PSHfiJjantZqnS7Qjbsa3cHAUMx9cca97633nfzEnRH3fCDxXUJmQl5eogh7D-V0JmnGOasbs_ECtIjGzMIK1qxkIPpFMvNBe-HFPsPYz5g8mnCgSfsq6D_CLrfjug="},
                    # {"phone": "***********", "account_key": "1AZWarzYBu0YIzGbndgMFh7Mdk1Q7l33ddsNB18yXn71jKqpmF7EL1c9kaoFtKY8TFc-885-LbnlCDeZJeLOI8-mzDbDFUGjVPIfIZvZ6Ti041pyRkSM8tHQWwSXEzv7nl8s4NnwOHv0OlTB7kQzALMct_4CqnU4huDHp7yF56C0X7TTSvylH0r2cVXXia5R151G1ZQ7ssHzBFzMWoBZlXZAUOfChCRhlJMnJSWfp_a8rIMVqe9ggJewi5hDAbjPZIcNndcXXzzXnEbPjTjD8Cz7Ghk1XNOj6BMntpu-cnfnO6_pArZWcenHTcNbA5s4XlrfmpxVUr7yklyWIHtJeagESm0E6SE4="}
                ]
                for account_info in accounts:
                    # httpx.get("http://**************:486/proxy/changePortIp?key=77356d325e788eba6e9b80d25717df3a&port=5002")
                    # proxy = (python_socks.ProxyType.HTTP, "**************", 5002)
                    proxy = (python_socks.ProxyType.HTTP, "localhost", 10809)
                    phone = account_info.get("phone")
                    send_num_key = self.send_num_key.format(phone)
                    number_of_requests = await self.redis_client.get(send_num_key) or 0
                    if int(number_of_requests) < self.send_max:
                        try:
                            # await self.contact_ban(account_info, proxy)
                            # await self.send_private_message(account_info, proxy)
                            await self.send_public_messages(account_info, proxy)
                            # await self.contact_ban(account_info, proxy)
                        except (Exception,) as e:
                            logger.exception(e)
                    else:
                        logger.warning(f"【{phone}】今日请求次数已经消耗完了,共请求了【{number_of_requests}】次")
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 10)


if __name__ == "__main__":
    send_message = SendMessage()
    asyncio.run(send_message.start_crawler())
