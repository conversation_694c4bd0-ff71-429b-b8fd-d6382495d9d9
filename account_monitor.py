# -*- coding: utf-8 -*-
# @Time : 2023/11/14 11:01
# @Site : 
# @File : account_monitor.py
# @Software: PyCharm
import json
import asyncio
import python_socks
from loguru import logger
from datetime import timezone
from telethon.sessions import StringSession
from telethon import TelegramClient, events, utils
from telethon.tl.functions.channels import GetFullChannelRequest
from telethon.tl.types import InputMessagesFilterPinned
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.functions.messages import GetFullChatRequest

from utils.crawl_tools import parse_message, download_media
from utils.config import msg_kafka_key, api_id, api_hash, error_key, redis_client, log_path, oss_base_path, save_url, media_avatar_redis_client, sender_key


class AccountMonitor(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        self.account_key = "tg_spider:monitor_account:{}"
        self.group_info_key = "tg_spider:group_info:{}"

    @staticmethod
    async def login(account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群监控登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except:
            error_mess = f"群监控登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            logger.error(error_mess)
            await redis_client.sadd(error_key, error_mess)

    async def monitor(self, account_info, machine_ip):
        """
        获取群的实时消息
        :return:
        """
        phone = account_info.get("phone")
        # client = await self.login(account_info, machine_ip)
        # 本地测试登录
        proxy = (python_socks.ProxyType.HTTP, "localhost", 10809)
        client = TelegramClient("./test_session/*************.session", api_id, api_hash, proxy=proxy, timeout=10)
        await client.connect()
        is_user = await client.is_user_authorized()
        if not is_user:
            print(">>>>>>>>>>>>>>>>>>>>登录失败")
            client.disconnect()
        else:
            print("success")

        logger.info(f"{phone} 开始监听新消息")

        @client.on(events.NewMessage())
        async def my_event_handler(event):
            try:
                # 群聊
                if event.is_private is False:
                    chat = await event.get_chat()
                    chat_dict = chat.to_dict()
                    channel_id = chat_dict.get("id")
                    channel_name = chat_dict.get("title")
                    category_type = chat_dict.get("_")
                    # 公开链接
                    if category_type == "Channel":
                        if chat_dict.get("broadcast"):
                            category = "broadcast"
                        else:
                            category = "group"
                        group_info_key = self.group_info_key.format(channel_id)
                        channel_info = await redis_client.smembers(group_info_key)
                        if not channel_info:
                            full_info = await client(GetFullChannelRequest(chat))
                            channel = f"https://t.me/{full_info.chats[0].username}"
                            await redis_client.sadd(group_info_key, channel)
                        else:
                            channel = list(channel_info).pop()
                        message_dict = await parse_message(client, event.message, channel_id, channel, channel_name)
                        message_dict["category"] = category
                        await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": message_dict}, ensure_ascii=False))
                    # 私密链接
                    else:
                        category = "private_group"
                        private_group_info_crawl = await redis_client.get(f"tg_spider:group_info:{channel_id}")
                        if not private_group_info_crawl:
                            await self.get_private_group_info(client, chat)
                        message_dict = await parse_message(client, event.message, channel_id, "", chat.title)
                        message_dict["category"] = "private_group"
                        message_dict["msgId"] = message_dict["sendTime"]
                        await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": message_dict}, ensure_ascii=False))
                    logger.info(f"监听到 {channel_name} 的消息,群类型是 {category}")
                else:
                    # 私聊
                    private_message_dict = await self.parse_private_message(client, event.message)
                    await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "user_msg", "Body": private_message_dict}, ensure_ascii=False))
                    logger.info(f"监听到{private_message_dict['senderNickName']} 和 {private_message_dict['receiverNickName']} 的私聊")
            except (Exception,) as e:
                logger.exception(e)
                logger.warning(event.to_json())

        await client.run_until_disconnected()

    async def parse_private_message(self, client, message):
        """
        解析私聊 1对1数据
        :param client:
        :param message:
        :return:
        """
        message_dict = message.to_dict()
        msg_id = message_dict.get("id")
        send_time = int(message_dict.get("date").replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
        content = message_dict.get("message")
        sender = await message.get_sender()
        send_nick_name = str(utils.get_display_name(message.sender))
        sender_dict = sender.to_dict()
        send_phone = sender.phone
        sender_id = sender.id
        sender_unique_id_key = sender_key.format(sender_id)
        sender_avatar = await media_avatar_redis_client.get(sender_unique_id_key)
        if not sender_avatar:
            sender_avatar_byte = await client.download_profile_photo(entity=sender, file=bytes)
            if sender_avatar_byte:
                oss_path = oss_base_path.format(f"/sender/{sender_id}.jpg")
                # upload_success = await upload_file_to_oss(oss_path, sender_avatar_byte)
                # if not upload_success:
                #     await redis_client.sadd(error_key, f"发送人【{sender_id}】头像上传失败")
                sender_avatar = save_url.format(oss_path)
                await media_avatar_redis_client.set(sender_unique_id_key, sender_avatar, nx=True, ex=1 * 60 * 60 * 24)
        status = sender_dict.get("status")
        sender_online_time = None
        if status and status.get("was_online"):
            if status.get("was_online"):
                sender_online_time = int(status.get("was_online").replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
            elif status.get("expires"):
                sender_online_time = int(status.get("expires").replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
        receiver_info = await self.get_receiver_info(client, message.peer_id.user_id)
        private_message_dict = {
            "msgId": msg_id,
            "sendTime": send_time,
            "content": content,
            "senderUid": sender_id,
            "category": "user_msg",
            "senderPhone": send_phone,
            "senderNickName": send_nick_name,
            "senderAvatar": sender_avatar,
            "senderOnlineTime": sender_online_time
        }
        private_message_dict.update(receiver_info)
        media_json = message_dict.get("media")
        if media_json:
            message_media_id, media_info, media_type = await download_media(client, message, media_json, f"私聊_{sender_id}_{receiver_info['receiverUid']}")
            private_message_dict["media"] = media_info
            private_message_dict["mediaType"] = media_type
            private_message_dict["mediaId"] = message_media_id
        return private_message_dict

    @staticmethod
    async def get_receiver_info(client, user_id):
        """
        获取私聊接收方信息
        :param client:
        :param user_id:
        :return:
        """
        receiver = await client(GetFullUserRequest(id=user_id))
        receiver_dict = receiver.users[0].to_dict()
        receiver_uid = receiver_dict.get("id")
        receiver_phone = receiver_dict.get("phone")
        receiver_nick_name = str(utils.get_display_name(receiver.users[0]))
        sender_unique_id_key = sender_key.format(receiver_uid)
        receiver_avatar = await media_avatar_redis_client.get(sender_unique_id_key)
        if not receiver_avatar:
            receiver_avatar_byte = await client.download_profile_photo(entity=receiver.users[0], file=bytes)
            if receiver_avatar_byte:
                oss_path = oss_base_path.format(f"/sender/{user_id}.jpg")
                # upload_success = await upload_file_to_oss(oss_path, receiver_avatar_byte)
                # if not upload_success:
                #     await redis_client.sadd(error_key, f"私聊人【{user_id}】头像上传失败")
                receiver_avatar = save_url.format(oss_path)
                await media_avatar_redis_client.set(sender_unique_id_key, receiver_avatar, nx=True, ex=1 * 60 * 60 * 24)
        receiver_online_time = None
        status = receiver_dict.get("status")
        if status:
            if status.get("was_online"):
                receiver_online_time = int(status.get("was_online").replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
            elif status.get("expires"):
                receiver_online_time = int(status.get("expires").replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
        return {
            "receiverUid": receiver_uid,
            "receiverPhone": receiver_phone,
            "receiverNickName": receiver_nick_name,
            "receiverAvatar": receiver_avatar,
            "receiverOnlineTime": receiver_online_time,
        }

    @staticmethod
    async def get_private_group_participants(client, chat, full_json):
        """
        获取私聊群的成员
        :param client:
        :param chat:
        :param full_json:
        :return:
        """
        admins = {str(_['user_id']): _['_'] for _ in full_json.get("participants").get("participants") if _['_'] != "ChatParticipant"}
        async for user in client.iter_participants(chat):
            user_dict = user.to_dict()
            user_id = user_dict.get("id") or ""
            user_type = "Private" + admins.get(str(user_id), "")
            first_name = user_dict.get("first_name")
            last_name = user_dict.get("last_name")
            user_name = user_dict.get("username")
            phone = user_dict.get("phone")
            status = user_dict.get("status") or {}
            online_time = status.get("was_online", status.get("expires"))
            if online_time:
                online_time = int(online_time.replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp() * 1000)
            unique_id = f"{chat.id}_{user_id}"
            sender_unique_id_key = sender_key.format(unique_id)
            sender_avatar = await media_avatar_redis_client.get(sender_unique_id_key)
            sender_avatar_byte = await client.download_profile_photo(entity=user, file=bytes)
            if sender_avatar_byte:
                oss_path = oss_base_path.format(f"/sender/{chat.id}/{user_id}.jpg")
                # upload_success = await upload_file_to_oss(oss_path, sender_avatar_byte)
                # if not upload_success:
                #     await redis_client.sadd(error_key, f"私密群成员【{user_id}】头像上传失败")
                sender_avatar = save_url.format(oss_path)
                await media_avatar_redis_client.set(sender_unique_id_key, sender_avatar, nx=True, ex=3 * 60 * 60 * 24)
            user_info = {"firstName": first_name, "lastName": last_name, "userName": user_name, "phone": phone, "avatar": sender_avatar, "onLineTime": online_time, "userId": user_id, "channelId": chat.id, "userType": user_type}
            user_message = {"Key": "sender", "Body": user_info}
            await redis_client.sadd(msg_kafka_key, json.dumps(user_message, ensure_ascii=False))

    async def get_private_group_info(self, client, chat):
        """
        获取私密群基础信息
        :param client:
        :param chat:
        :return:
        """
        _id = chat.id
        full_chat = await client(GetFullChatRequest(_id))
        full_json = full_chat.to_dict().get('full_chat')
        desc = full_json.get("about")
        member_count = chat.participants_count
        photo_bytes = await client.download_profile_photo(entity=chat, file=bytes)
        cover_img = ""
        if photo_bytes:
            oss_path = oss_base_path.format(f"/channel/{chat.id}.jpg")
            # upload_success = await upload_file_to_oss(oss_path, photo_bytes)
            # if not upload_success:
            #     await redis_client.sadd(error_key, f"私密群组【{chat.title}】头像上传失败")
            cover_img = save_url.format(oss_path)
        # 获取置顶消息
        logger.info(f"开始采集私密群置顶消息")
        ids = ""
        async for message in client.iter_messages(entity=chat, filter=InputMessagesFilterPinned, limit=1000):
            try:
                pinned_message_dict = await parse_message(client, message, _id, "", chat.title)
                pinned_message_dict["category"] = "private_group"
                pinned_message_dict["msgId"] = pinned_message_dict["sendTime"]
                await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": pinned_message_dict}, ensure_ascii=False))
                ids += str(pinned_message_dict["msgId"]) + ","
            except (Exception,) as e:
                logger.error(e)
        ids = ids.strip(",")
        # 获取参与者
        logger.info(f"开始采集私密群成员")
        await self.get_private_group_participants(client, chat, full_json)
        group_info = {
            "name": chat.title,
            "desc": desc,
            "memberCount": member_count,
            "category": 'private_group',
            "origin": "app",
            "id": _id,
            "coverImg": cover_img,
            "topMsgIds": ids
        }
        logger.info("私密群基础信息采集完成")
        await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "channel", "Body": group_info}, ensure_ascii=False))
        await redis_client.set(f"tg_spider:group_info:{_id}", "success")
        await redis_client.expire(f"tg_spider:group_info:{_id}", 24 * 60 * 60)

    async def crawl(self):
        await self.monitor({"phone": "*************"}, "1")


if __name__ == '__main__':
    account_monitor = AccountMonitor()
    asyncio.run(account_monitor.monitor({"phone": "*************"}, "1"))
