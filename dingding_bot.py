# -*- coding: utf-8 -*-
# @Time : 2024/3/3 10:16
# @Site : 
# @File : dingding_bot.py
# @Software: PyCharm
import json
import httpx
import asyncio

from utils.config import redis_client
from apscheduler.schedulers.asyncio import AsyncIOScheduler


async def scan_get_all_key(match, count=10000):
    key_names = []
    cursor = 0
    while True:
        cursor, keys = await redis_client.scan(cursor=cursor, match=match, count=count)
        for key in keys:
            key_names.append(key)
        if cursor == 0:
            break
    return key_names


async def promotion_assistant():
    webhook = "https://oapi.dingtalk.com/robot/send?access_token=0388194411382eb327c897eda4e93b4e2725113200975e96de1c72f829a8ece7"
    ad_today_count = await redis_client.get("tg_spider:promotion:ad_today_count")
    accounts_number = await redis_client.scard("tg_spider:promotion:account")
    official_banned_count = len(await scan_get_all_key("tg_spider:promotion:temp_ban:*"))
    total_key = await scan_get_all_key("tg_spider:promotion:ad_send_total_count:*")
    total = 0
    for key in total_key:
        number = await redis_client.get(key)
        total += int(number)
    content = f"当前推广账号池剩余账号数量为【{accounts_number}】,其中【{official_banned_count}】个账号处于官方全局禁言中,推广消息历史总发送数量为【{total}】,今日发送【{ad_today_count}】"
    message = {"msgtype": "text", "text": {"content": content}}
    header = {"Content-Type": "application/json"}
    print(content)
    httpx.post(url=webhook, data=json.dumps(message), headers=header)


async def phone_assistant():
    webhook = "https://oapi.dingtalk.com/robot/send?access_token=0388194411382eb327c897eda4e93b4e2725113200975e96de1c72f829a8ece7"
    remaining_tasks_count = await redis_client.scard("tg_spider:phone_search:task")
    account_count = await redis_client.scard("tg_spider:phone_search:account")
    account_used = len(await scan_get_all_key("tg_spider:phone_search:phone_total:*"))
    total_number_of_searches = await redis_client.scard("tg_spider:phone_search:results")
    number_of_results = await redis_client.scard("tg_spider:phone_search:succ")
    percentage = (number_of_results / total_number_of_searches) * 100
    content = "当前手机号搜索账号池剩余账号数量【{}】,共搜索了【 {} 】次, 平均每个账号请求【{}】次, 落实人数:【 {} 】, 搜得率:【 {:.2f}% 】, 剩余任务数:【 {} 】".format(account_count, total_number_of_searches, int(total_number_of_searches / account_used), number_of_results, percentage, remaining_tasks_count)
    message = {"msgtype": "text", "text": {"content": content}}
    header = {"Content-Type": "application/json"}
    httpx.post(url=webhook, data=json.dumps(message), headers=header)


if __name__ == '__main__':
    executor = AsyncIOScheduler(
        timezone='Asia/Shanghai',
        job_defaults={
            "misfire_grace_time": 3600,  # 允许存在多久时间的任务数
            "max_instances": 200,  # 该定时任务允许最大的实例个数
            "coalesce": False  # 多个任务堆积，是否运行最新的
        }
    )
    executor.add_job(promotion_assistant, trigger='cron', day_of_week='*', hour=18, minute='00', second='00', coalesce=False, misfire_grace_time=3600)
    executor.add_job(phone_assistant, trigger='cron', day_of_week='*', hour='0-19/9', minute='00', second='00', coalesce=False, misfire_grace_time=3600)
    executor.start()
    try:
        asyncio.get_event_loop().run_forever()
    except (KeyboardInterrupt, SystemExit):
        pass
