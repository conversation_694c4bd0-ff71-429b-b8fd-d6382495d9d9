#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram StringSession 获取工具
支持多种获取方式：手机号登录、二维码登录、批量生成等
"""

import asyncio
import json
import random
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, PhoneNumberInvalidError
from utils.developer_infos import developer_list

# 默认API配置（如果developer_list不可用）
DEFAULT_API_ID = 2040
DEFAULT_API_HASH = 'b18441a1ff607e10a989891a5462e627'

class StringSessionGenerator:
    def __init__(self, api_id=None, api_hash=None, use_random_api=True):
        """
        初始化StringSession生成器

        Args:
            api_id: Telegram API ID
            api_hash: Telegram API Hash
            use_random_api: 是否使用随机API（从developer_list中选择）
        """
        if use_random_api:
            try:
                developer_info = random.choice(developer_list)
                self.api_id = int(developer_info["api_id"])
                self.api_hash = developer_info["api_hash"]
                print(f"使用随机API: {self.api_id}")
            except:
                self.api_id = api_id or DEFAULT_API_ID
                self.api_hash = api_hash or DEFAULT_API_HASH
                print(f"使用默认API: {self.api_id}")
        else:
            self.api_id = api_id or DEFAULT_API_ID
            self.api_hash = api_hash or DEFAULT_API_HASH

    async def get_session_by_phone(self, phone_number, proxy=None):
        """
        通过手机号获取StringSession

        Args:
            phone_number: 手机号（包含国家代码，如+86）
            proxy: 代理配置（可选）

        Returns:
            dict: 包含phone, account_key的字典
        """
        client = TelegramClient(StringSession(), self.api_id, self.api_hash, proxy=proxy)

        try:
            await client.connect()
            print(f"开始为手机号 {phone_number} 获取StringSession...")

            # 发送验证码
            await client.send_code_request(phone_number)
            print(f"验证码已发送到 {phone_number}")

            # 输入验证码
            code = input("请输入收到的验证码: ").strip()

            try:
                # 尝试登录
                await client.sign_in(phone_number, code)
            except SessionPasswordNeededError:
                # 需要两步验证密码
                password = input("请输入两步验证密码: ").strip()
                await client.sign_in(password=password)

            # 获取StringSession
            session_string = client.session.save()

            # 获取用户信息验证
            me = await client.get_me()
            print(f"✅ 登录成功！用户: {me.first_name} (@{me.username})")

            result = {
                "phone": phone_number,
                "account_key": session_string,
                "user_id": me.id,
                "username": me.username,
                "first_name": me.first_name
            }

            print(f"StringSession: {session_string}")
            return result

        except PhoneNumberInvalidError:
            print(f"❌ 手机号 {phone_number} 无效")
            return None
        except PhoneCodeInvalidError:
            print("❌ 验证码无效")
            return None
        except Exception as e:
            print(f"❌ 获取StringSession失败: {e}")
            return None
        finally:
            await client.disconnect()

    async def get_session_by_qr(self, proxy=None):
        """
        通过二维码获取StringSession

        Args:
            proxy: 代理配置（可选）

        Returns:
            dict: 包含account_key等信息的字典
        """
        client = TelegramClient(StringSession(), self.api_id, self.api_hash, proxy=proxy)

        try:
            await client.connect()
            print("开始通过二维码获取StringSession...")

            # 生成二维码登录
            qr_login = await client.qr_login()

            print("请使用Telegram手机客户端扫描以下二维码:")
            print("1. 打开Telegram手机客户端")
            print("2. 进入 设置 > 设备 > 扫描二维码")
            print("3. 扫描下方二维码")
            print("\n" + "="*50)

            # 显示二维码（需要安装qrcode库）
            try:
                import qrcode
                qr = qrcode.QRCode(version=1, box_size=1, border=1)
                qr.add_data(qr_login.url)
                qr.make(fit=True)
                qr.print_ascii(invert=True)
            except ImportError:
                print(f"二维码URL: {qr_login.url}")
                print("提示: 安装qrcode库可显示二维码图形")

            print("="*50)
            print("等待扫码登录...")

            # 等待登录
            await qr_login.wait()

            # 获取StringSession
            session_string = client.session.save()

            # 获取用户信息
            me = await client.get_me()
            print(f"✅ 登录成功！用户: {me.first_name} (@{me.username})")

            result = {
                "phone": me.phone,
                "account_key": session_string,
                "user_id": me.id,
                "username": me.username,
                "first_name": me.first_name
            }

            print(f"StringSession: {session_string}")
            return result

        except Exception as e:
            print(f"❌ 二维码登录失败: {e}")
            return None
        finally:
            await client.disconnect()

    async def batch_generate_sessions(self, phone_numbers, proxy=None):
        """
        批量生成StringSession

        Args:
            phone_numbers: 手机号列表
            proxy: 代理配置（可选）

        Returns:
            list: 成功生成的session列表
        """
        results = []

        for i, phone in enumerate(phone_numbers, 1):
            print(f"\n{'='*60}")
            print(f"处理第 {i}/{len(phone_numbers)} 个账号: {phone}")
            print('='*60)

            # 为每个账号使用不同的API（避免限制）
            if len(developer_list) > 1:
                developer_info = random.choice(developer_list)
                self.api_id = int(developer_info["api_id"])
                self.api_hash = developer_info["api_hash"]
                print(f"使用API: {self.api_id}")

            result = await self.get_session_by_phone(phone, proxy)
            if result:
                results.append(result)
                print(f"✅ {phone} 处理成功")
            else:
                print(f"❌ {phone} 处理失败")

            # 添加延迟避免频率限制
            if i < len(phone_numbers):
                print("等待5秒后处理下一个账号...")
                await asyncio.sleep(5)

        return results

    def save_sessions_to_file(self, sessions, filename="telegram_accounts.json"):
        """
        保存sessions到文件

        Args:
            sessions: session列表
            filename: 文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(sessions, f, ensure_ascii=False, indent=2)
            print(f"✅ 账号信息已保存到 {filename}")
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")

# 兼容原有函数
async def get_session_string():
    """兼容原有的简单获取函数"""
    generator = StringSessionGenerator()
    phone = input("请输入手机号（包含国家代码，如+*************）: ").strip()
    result = await generator.get_session_by_phone(phone)
    return result["account_key"] if result else None

async def main():
    """主函数 - 提供交互式菜单"""
    generator = StringSessionGenerator()

    print("🔐 Telegram StringSession 获取工具")
    print("="*50)
    print("1. 通过手机号获取StringSession")
    print("2. 通过二维码获取StringSession")
    print("3. 批量生成StringSession")
    print("4. 验证现有StringSession")
    print("5. 退出")
    print("="*50)

    while True:
        choice = input("\n请选择操作 (1-5): ").strip()

        if choice == "1":
            await handle_phone_login(generator)
        elif choice == "2":
            await handle_qr_login(generator)
        elif choice == "3":
            await handle_batch_generation(generator)
        elif choice == "4":
            await handle_session_validation(generator)
        elif choice == "5":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

async def handle_phone_login(generator):
    """处理手机号登录"""
    phone = input("请输入手机号（包含国家代码，如+*************）: ").strip()

    # 询问是否使用代理
    use_proxy = input("是否使用代理？(y/n): ").strip().lower() == 'y'
    proxy = None
    if use_proxy:
        proxy_host = input("代理主机: ").strip()
        proxy_port = int(input("代理端口: ").strip())
        proxy_username = input("代理用户名（可选）: ").strip() or None
        proxy_password = input("代理密码（可选）: ").strip() or None

        proxy = {
            'proxy_type': 'socks5',  # 或 'http'
            'addr': proxy_host,
            'port': proxy_port,
            'username': proxy_username,
            'password': proxy_password
        }

    result = await generator.get_session_by_phone(phone, proxy)
    if result:
        save_choice = input("是否保存到文件？(y/n): ").strip().lower()
        if save_choice == 'y':
            filename = input("文件名（默认: account.json）: ").strip() or "account.json"
            generator.save_sessions_to_file([result], filename)

async def handle_qr_login(generator):
    """处理二维码登录"""
    print("\n📱 二维码登录模式")
    result = await generator.get_session_by_qr()
    if result:
        save_choice = input("是否保存到文件？(y/n): ").strip().lower()
        if save_choice == 'y':
            filename = input("文件名（默认: qr_account.json）: ").strip() or "qr_account.json"
            generator.save_sessions_to_file([result], filename)

async def handle_batch_generation(generator):
    """处理批量生成"""
    print("\n📋 批量生成模式")
    print("请输入手机号列表（每行一个，输入空行结束）:")

    phones = []
    while True:
        phone = input("手机号: ").strip()
        if not phone:
            break
        phones.append(phone)

    if not phones:
        print("❌ 没有输入任何手机号")
        return

    print(f"将处理 {len(phones)} 个手机号")
    confirm = input("确认继续？(y/n): ").strip().lower()
    if confirm != 'y':
        return

    results = await generator.batch_generate_sessions(phones)

    if results:
        print(f"\n✅ 成功生成 {len(results)} 个StringSession")
        filename = input("保存文件名（默认: batch_accounts.json）: ").strip() or "batch_accounts.json"
        generator.save_sessions_to_file(results, filename)
    else:
        print("❌ 没有成功生成任何StringSession")

async def handle_session_validation(generator):
    """验证现有StringSession"""
    print("\n🔍 StringSession验证模式")
    session_string = input("请输入StringSession: ").strip()

    if not session_string:
        print("❌ StringSession不能为空")
        return

    client = TelegramClient(StringSession(session_string), generator.api_id, generator.api_hash)

    try:
        await client.connect()

        if await client.is_user_authorized():
            me = await client.get_me()
            print(f"✅ StringSession有效！")
            print(f"用户ID: {me.id}")
            print(f"用户名: @{me.username}")
            print(f"姓名: {me.first_name} {me.last_name or ''}")
            print(f"手机号: {me.phone}")
        else:
            print("❌ StringSession无效或已过期")

    except Exception as e:
        print(f"❌ 验证失败: {e}")

    finally:
        await client.disconnect()

# 快速使用函数
async def quick_generate_session(phone_number, api_id=None, api_hash=None):
    """
    快速生成StringSession的便捷函数

    Args:
        phone_number: 手机号
        api_id: API ID（可选）
        api_hash: API Hash（可选）

    Returns:
        str: StringSession字符串
    """
    generator = StringSessionGenerator(api_id, api_hash)
    result = await generator.get_session_by_phone(phone_number)
    return result["account_key"] if result else None

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())