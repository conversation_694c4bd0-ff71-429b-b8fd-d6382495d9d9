# -*- coding: utf-8 -*-
# @Time : 2023/12/14 16:36
# @Site :
# @File : group_monitor_tools.py
# @Software: PyCharm
import re
import json
import copy

import aioredis
import httpx
import random
import asyncio
from loguru import logger
from telethon import Telegram<PERSON>lient
from telethon.sessions import StringSession
from telethon.tl.types import MessageActionChatAddUser, User
from telethon.tl.functions.messages import ImportChatInviteRequest
from telethon.tl.functions.channels import JoinChannelRequest, GetFullChannelRequest, LeaveChannelRequest

from utils.crawl_tools import get_machine_ip, get_group_info
from utils.config import api_id, api_hash, error_key, log_path, failure_key, redis_host, redis_port, redis_password


class GroupMonitorTools(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # redis客户端
        redis_pool = aioredis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, health_check_interval=30)
        self.redis_client = aioredis.Redis(connection_pool=redis_pool)
        # 账号的key
        self.account_key = "tg_spider:monitor_account:{}"
        self.banned_key = "tg_spider:monitor_banned"
        # 群组基础信息的key
        self.group_info_key = "tg_spider:group_info"
        # 账号加群信息的key
        self.group_join_info_key = "tg_spider:group_join_info"
        self.group_join_succ_key = "tg_spider:group_join_succ"
        self.group_join_verification_key = "tg_spider:group_info_verification"
        # 账号每日加群数量统计
        self.today_join_number = "tg_spider:today_join_group_count:{}"
        # 数据库群组最大的消息偏移id
        self.offset_key = "tg_spider:crawl:max_offset"
        # 需要加群的链接key
        self.join_task_key = "tg_spider:politics_links"
        # 需要退群的链接key
        self.leave_task_key = "tg_spider:group_leave"
        # 加群的cd
        self.join_cd = 0
        # 加群的最大数量
        self.join_group_max_number = 0
        # 单日加群最大量
        self.today_join_max_number = 0

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群管理登录失败,服务器地址【{machine_ip}】,账号【{phone}】被封禁"
                logger.error(error_mess)
                await self.redis_client.sadd(self.banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)

    async def get_all_join_group(self, account_info, machine_ip):
        """获取账号加入的所有群信息"""
        phone = account_info.get("phone")
        group_join_info = []
        client = await self.login(account_info, machine_ip)
        dialogs = await client.get_dialogs()
        for dialog in dialogs:
            channel_id = dialog.entity.id
            if dialog.is_user:
                continue
            else:
                if await self.redis_client.sismember("tg_spider:group_join_succ_already", channel_id):
                    if hasattr(dialog.entity, "username"):
                        await client(LeaveChannelRequest(dialog))
                    else:
                        await client.delete_dialog(dialog)
                    continue
                if hasattr(dialog.entity, "username") and dialog.entity.username:
                    user_name = dialog.entity.username
                    channel = f"https://t.me/{user_name}"
                else:
                    channel = await self.redis_client.hget(self.group_info_key, channel_id)

                    await get_group_info(client, channel, dialog.entity)
                await self.redis_client.sadd("tg_spider:group_join_succ_already", channel_id)
                group_join_info.append({"id": channel_id, "link": channel})
        await self.redis_client.hset(self.group_join_info_key, phone, json.dumps(group_join_info, ensure_ascii=False))
        logger.info(f"【{phone}】 共加入 【{len(group_join_info)}】个群")
        return group_join_info

    async def leave_group(self, account_info, machine_ip):
        """
        退群
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        while True:
            phone = account_info.get("phone")
            # 拿到需要离开的群组列表
            leave_group_str = await self.redis_client.hget(self.leave_task_key, phone) or "[]"
            leave_group_infos = json.loads(leave_group_str)
            logger.info(f"【{phone}】需要退【{len(leave_group_infos)}】个群")
            if len(leave_group_infos) < 1:
                await self.redis_client.hdel(self.leave_task_key, phone)
                break
            new_leave_group_infos = copy.deepcopy(leave_group_infos)
            # 拿到加群的列表
            group_join_str = await self.redis_client.hget(self.group_join_info_key, phone) or "[]"
            group_join_info = json.loads(group_join_str)
            # 开始登录账号
            client = await self.login(account_info, machine_ip)
            assert client
            # 如果账号已经加入的群组在最大加群数量以下,并且今日加群次数少于指定值,就可以进行加群
            for leave_group_info in leave_group_infos:
                group_id = leave_group_info.get('id')
                group_link = leave_group_info.get('link')
                try:
                    entity = await client.get_entity(group_link)
                    await client(LeaveChannelRequest(entity))
                    group_join_info.remove(leave_group_info)
                    new_leave_group_infos.remove(leave_group_info)
                    await self.redis_client.srem("tg_spider:group_join_succ", group_id)
                except (Exception,) as e:
                    logger.error(e)
                    if "The target user is not a member of the specified megagroup or channel" in str(e):
                        if leave_group_info in group_join_info:
                            group_join_info.remove(leave_group_info)
                        new_leave_group_infos.remove(leave_group_info)
                        await self.redis_client.sadd(self.group_join_verification_key, group_link)
                        await self.redis_client.srem("tg_spider:group_join_succ", group_id)
                    elif "The channel specified is private and you lack permission to access it. " in str(e):
                        if leave_group_info in group_join_info:
                            group_join_info.remove(leave_group_info)
                        new_leave_group_infos.remove(leave_group_info)
                        await self.redis_client.sadd(self.group_join_verification_key, group_link)
                        await self.redis_client.srem("tg_spider:group_join_succ", group_id)
                    elif "A wait of " in str(e):
                        await self.redis_client.hset(self.join_task_key, group_id, group_link)
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            await asyncio.sleep(wait_time_int)
                    elif "No user has" in str(e) or "Nobody is using this username" in str(e):
                        if leave_group_info in group_join_info:
                            group_join_info.remove(leave_group_info)
                        new_leave_group_infos.remove(leave_group_info)
                        await self.redis_client.srem("tg_spider:group_join_succ", group_id)
                    else:
                        await self.redis_client.hset(self.join_task_key, group_id, group_link)
                finally:
                    await self.redis_client.hset(self.group_join_info_key, phone, json.dumps(group_join_info, ensure_ascii=False))
                    await self.redis_client.hset(self.leave_task_key, phone, json.dumps(new_leave_group_infos, ensure_ascii=False))
                    await asyncio.sleep(10)

    async def join_group(self, account_info, machine_ip):
        """
        加群
        :param account_info: 账号信息
        :param machine_ip:   本机ip
        :return:
        """
        while True:
            phone = account_info.get("phone")
            task_balance = await self.redis_client.hlen(self.join_task_key)
            if not task_balance:
                break
            # 先拿到账号已经加入的群组列表,判断账号加群的数量
            group_join_str = await self.redis_client.hget(self.group_join_info_key, phone) or "[]"
            group_join_info = json.loads(group_join_str)
            join_group_number = len(group_join_info)
            # 再拿到今日账号加群次数
            today_join_number_key = self.today_join_number.format(phone)
            today_join_number = await self.redis_client.get(today_join_number_key) or 0
            logger.info(f"【{phone}】 当前加群总量是【{join_group_number}】,今日加群【{today_join_number}】")
            if join_group_number >= self.join_group_max_number or int(today_join_number) >= self.today_join_max_number:
                break
            # 开始登录账号
            client = await self.login(account_info, machine_ip)
            assert client
            # 如果账号已经加入的群组在最大加群数量以下,并且今日加群次数少于指定值,就可以进行加群
            while join_group_number < self.join_group_max_number and int(today_join_number) < self.today_join_max_number:
                # 判断加群是否成功的
                join_succ = True
                # 随机选取一个加群任务
                task_balance = await self.redis_client.hkeys(self.join_task_key)
                if not task_balance:
                    break
                group_id = random.choice(task_balance)
                group_link = await self.redis_client.hget(self.join_task_key, group_id)
                if not group_link:
                    await self.redis_client.hdel(self.join_task_key, group_id)
                issucc = await self.redis_client.sismember(self.group_join_succ_key, group_id)
                isverification = await self.redis_client.sismember(self.group_join_verification_key, group_link)
                isfailure = await self.redis_client.sismember(failure_key, group_link)
                join_status = None
                if issucc or isverification or isfailure:
                    await self.redis_client.hdel(self.join_task_key, group_id)
                    continue
                try:
                    if "joinchat" in group_link or "+" in group_link:
                        if "joinchat" in group_link:
                            hash_str = group_link.replace("https://t.me/joinchat/", "")
                        else:
                            hash_str = re.search("(?<=\+).*", group_link).group()
                        join_status = await client(ImportChatInviteRequest(hash_str))
                    entity = await client.get_entity(group_link)
                    group_id = entity.id
                    # 避免重复加群
                    await self.redis_client.hdel(self.join_task_key, group_id)
                    if isinstance(entity, User):
                        continue
                    # 这些群组的附件需要存储,需要单独记录到一个key里面,用于download_media方法里面判断使用
                    await self.redis_client.hset(self.group_info_key, group_id, group_link)
                    group_info = await get_group_info(client, group_link, entity)
                    logger.info(group_info)
                    await self.redis_client.sadd("tg_spider:storage_group", group_id)
                    logger.info(f"用户【{phone}】开始加入【{group_link}】")
                    min_id = "0"
                    max_id = "0"
                    try:
                        full_channel_info = await client(GetFullChannelRequest(entity))
                        # 获取历史数据抓取节点最大的消息id作为补充数据的起点
                        min_id = await self.redis_client.hget(self.offset_key, entity.id) or 0
                        # 获取当前时间节点群消息最大的id作为补偿数据的止点
                        max_id = full_channel_info.full_chat.read_outbox_max_id
                    except (Exception,) as e:
                        logger.error(e)
                    # 有username就是公开链接,没有就是私有的
                    username = entity.username
                    if username:
                        join_status = await client(JoinChannelRequest(entity))
                    # 今日加群计数
                    if today_join_number == 0:
                        await self.redis_client.set(today_join_number_key, value="1", ex=24 * 60 * 60)
                    else:
                        await self.redis_client.incr(today_join_number_key)
                    if join_status:
                        updates = join_status.updates
                        # 频道没有验证问题,百分百关注成功,群组有可能有验证码要判断一下
                        if entity.broadcast is False:
                            # 加入成功后,需要监控后面的几条消息,看看是否有验证
                            if updates:
                                message = updates[-1]
                                action = message.message.action
                                message_id = message.message.id
                                if isinstance(action, MessageActionChatAddUser):
                                    # 判断加群后最新的5条消息,看看是否有弹出来验证码
                                    for i in range(1, 5):
                                        await asyncio.sleep(1)
                                        try:
                                            verification_message = await client.get_messages(entity, ids=message_id + i)
                                            if verification_message:
                                                content = verification_message.message or ""
                                                if bool(re.search('[^OTP]验证|限制|封禁|verification|回答', content)):
                                                    question = re.search("(?<=，)\d+.\d+(?=等于|=)", content)
                                                    if question:
                                                        if "x" in question:
                                                            question_str = question.group().replace("x", "*")
                                                        else:
                                                            question_str = question.group()
                                                        results = eval(question_str)
                                                        rows = verification_message.reply_markup.rows
                                                        for row_index, row in enumerate(rows):
                                                            for btn_index, btn in enumerate(row.buttons):
                                                                if btn.text == str(results):
                                                                    await verification_message.click(row_index, btn_index)
                                                                    join_succ = True
                                                                    webhook = "https://oapi.dingtalk.com/robot/send?access_token=74d5075165506b388b98df6cf60acc5107767d54da12c198d4b214d243eacc11"
                                                                    dingding_content = f"链接【 {group_link} 】 \n 群名【{entity.title}】 \n  触发加群验证码,验证码消息为: \n 【{content}】 计算结果为: \n 【{results}】,已经点击"
                                                                    dingding_message = {"msgtype": "text", "text": {"content": dingding_content}}
                                                                    header = {"Content-Type": "application/json"}
                                                                    httpx.post(url=webhook, data=json.dumps(dingding_message), headers=header)
                                                                    break
                                                    else:
                                                        # 加群验证码
                                                        webhook = "https://oapi.dingtalk.com/robot/send?access_token=74d5075165506b388b98df6cf60acc5107767d54da12c198d4b214d243eacc11"
                                                        dingding_content = f"链接【 {group_link} 】 \n 群名【{entity.title}】 \n 触发加群验证码,验证码消息为: \n 【{content}】"
                                                        dingding_message = {"msgtype": "text", "text": {"content": dingding_content}}
                                                        header = {"Content-Type": "application/json"}
                                                        httpx.post(url=webhook, data=json.dumps(dingding_message), headers=header)
                                                        join_succ = False
                                                    break
                                            else:
                                                await asyncio.sleep(0.2)
                                                continue
                                        except (Exception,) as e:
                                            logger.error(e)
                                            continue
                    # 成功加入,并且没有验证码或者已经通过了验证码
                    if join_succ:
                        logger.info(f"用户【{phone}】加入【{group_link}】成功,群名字是【{entity.title}】")
                        # 链接加入到加群的列表中用于记录账号加入的群聊
                        group_join_info.append({"id": group_id, "link": group_link})
                        await self.redis_client.hset(self.group_join_info_key, phone, json.dumps(group_join_info, ensure_ascii=False))
                        await self.redis_client.sadd(self.group_join_succ_key, group_id)
                        # 记录加入链接时候的消息id,用于补充中间缺失的消息
                        if min_id != max_id:
                            await self.redis_client.sadd("tg_spider:crawl:missing_data_info", json.dumps({"link": group_link, "min_id": str(min_id), "max_id": str(max_id)}))
                        await asyncio.sleep(self.join_cd)
                    else:
                        # 加入到加群需要验证的key,后面手动加群
                        await self.redis_client.sadd(self.group_join_verification_key, group_link)
                except (Exception,) as e:
                    logger.error(e)
                    if "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot find any entity corresponding" in str(e):
                        logger.error(f"【{group_link}】群不存在")
                        await self.redis_client.sadd(failure_key, group_link)
                        await self.redis_client.hdel(self.join_task_key, group_id)
                    elif "The chat the user tried to join has expired" in str(e):
                        logger.error(f"【{group_link}】用户尝试加入的聊天已过期，不再有效")
                        await self.redis_client.hdel(self.join_task_key, group_id)
                        await self.redis_client.sadd(failure_key, group_link)
                    elif "You have successfully requested to join this chat or channel" in str(e) or "The authenticated user is already a participant of the chat" in str(e) or "You have successfully requested to join this chat or channel":
                        await self.redis_client.hdel(self.join_task_key, group_id)
                        entity = await client.get_entity(group_link)
                        group_id = entity.id
                        await get_group_info(client, group_link, entity)
                        group_join_info.append({"id": group_id, "link": group_link})
                        await self.redis_client.hset(self.group_join_info_key, phone, json.dumps(group_join_info, ensure_ascii=False))
                        await self.redis_client.sadd(self.group_join_succ_key, group_id)
                    elif "A wait of " in str(e):
                        await self.redis_client.hset(self.join_task_key, group_id, group_link)
                        wait_time = re.search("\d+", str(e))
                        if wait_time:
                            wait_time_int = int(wait_time.group())
                            await asyncio.sleep(wait_time_int)
                    elif "The channel specified is private and you lack permission to access it. Another reason may be that you" in str(e):
                        await self.redis_client.sadd(self.group_join_verification_key, group_link)
                    elif "Cannot send requests while disconnected" in str(e):
                        await self.redis_client.hset(self.join_task_key, group_id, group_link)
                        return
                    else:
                        await self.redis_client.hset(self.join_task_key, group_id, group_link)
                finally:
                    join_group_number = len(group_join_info)
                    today_join_number = await self.redis_client.get(today_join_number_key) or 0
                    logger.info(f"【{phone}】今天已经加群【{today_join_number}】次")
            logger.info(f"【{phone}】 当前加群总量是【{join_group_number}】,今日加群【{today_join_number}】")

    async def deletion_already_join(self):
        while True:
            join_succ = await self.redis_client.smembers(self.group_join_succ_key)
            join_verification = await self.redis_client.smembers(self.group_join_verification_key)
            delete_ids = list(join_succ) + (list(join_verification))
            for delete_id in delete_ids:
                await self.redis_client.hdel(self.join_task_key, delete_id)
            await asyncio.sleep(60 * 60 * 1)

    async def start(self):
        while True:
            try:
                join_config_str = await self.redis_client.get("tg_spider:group_join_config")
                join_config = json.loads(join_config_str)
                self.join_cd = join_config["join_cd"]
                self.join_group_max_number = join_config["join_group_max_number"]
                self.today_join_max_number = join_config["today_join_max_number"]
                start = join_config["start"]
                if start:
                    task = join_config["task"]
                    machine_ip = get_machine_ip()
                    account_key = self.account_key.format(machine_ip)
                    account_list = await self.redis_client.get(account_key) or "[]"
                    account_infos = json.loads(account_list)
                    tasks = []
                    if task == "join":
                        tasks.extend([self.join_group(account_info, machine_ip) for account_info in account_infos])
                        await asyncio.gather(*tasks)
                    elif task == "leave":
                        tasks.extend([self.leave_group(account_info, machine_ip) for account_info in account_infos])
                        await asyncio.gather(*tasks)
                    else:
                        for account_info in account_infos:
                            await self.get_all_join_group(account_info, machine_ip)
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 30)

    async def repeat(self):
        machine_ip = get_machine_ip()
        account_key = self.account_key.format(machine_ip)
        account_list = await self.redis_client.get(account_key) or "[]"
        account_infos = json.loads(account_list)
        for account_info in account_infos:
            await self.get_all_join_group(account_info, machine_ip)


if __name__ == '__main__':
    group_join = GroupMonitorTools()
    asyncio.run(group_join.start())
