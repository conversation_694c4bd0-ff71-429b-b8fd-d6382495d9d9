#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
@project: tg_message_crawler
@file: repeat_group.py
@function:
@time: 2024/7/10 11:31
"""
import json
import time
import asyncio
from loguru import logger
from telethon.tl.types import Cha<PERSON>
from telethon import Telegram<PERSON>lient
from telethon.sessions import StringSession
from utils.crawl_tools import get_machine_ip, get_group_info
from telethon.tl.functions.channels import LeaveChannelRequest
from utils.config import redis_client, api_id, api_hash, log_path

logger.add(
    sink="%s_{time:YYYY_MM_DD}.log" % (log_path / "repeat_group"),
    encoding="utf8",
    format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
    rotation="00:00",
    retention="1 days"
)


async def login(account_info, machine_ip):
    phone = account_info.get("phone")
    try:
        string_session = account_info.get("account_key")
        client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
        try:
            await client.connect()
        except OSError:
            error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
        logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
        is_user = await client.is_user_authorized()
        if not is_user:
            error_mess = f"群管理登录失败,服务器地址【{machine_ip}】,账号【{phone}】被封禁"
            logger.error(error_mess)
            await redis_client.sadd("tg_spider:temp_banned", json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
        else:
            logger.success(f"【{phone}】登录成功")
            return client
    except (Exception,) as e:
        logger.error(e)


async def get_all_join_group(account_info, machine_ip):
    """获取账号加入的所有群信息"""
    phone = account_info.get("phone")
    group_join_info = []
    client = await login(account_info, machine_ip)
    dialogs = await client.get_dialogs()
    for dialog in dialogs:
        try:
            channel_id = dialog.entity.id
            if dialog.is_user:
                continue
            else:
                group_join_succ_already = await redis_client.sismember("tg_spider:group_join_succ_already", channel_id)
                if group_join_succ_already:
                    if hasattr(dialog.entity, "username"):
                        await client(LeaveChannelRequest(dialog))
                    else:
                        await client.delete_dialog(dialog)
                    continue
                if hasattr(dialog.entity, "username") and dialog.entity.username:
                    user_name = dialog.entity.username
                    channel = f"https://t.me/{user_name}"
                else:
                    channel = await redis_client.hget("tg_spider:group_info", channel_id)
                    entity = await client.get_entity(dialog.entity)
                    if isinstance(entity, Chat):
                        continue
                    if not channel:
                        time.sleep(5)
                        await get_group_info(client, channel, dialog.entity)
                await redis_client.sadd("tg_spider:group_join_succ_already", channel_id)
                group_join_info.append({"id": dialog.entity.id, "link": channel})
        except (Exception,) as e:
            logger.error(e)
    await redis_client.hset("tg_spider:group_info", phone, json.dumps(group_join_info, ensure_ascii=False))
    logger.info(f"【{phone}】 共加入 【{len(group_join_info)}】个群")
    return group_join_info


async def repeat():
    machine_ip = get_machine_ip()
    account_key = "tg_spider:monitor_account:{}".format(machine_ip)
    account_list = await redis_client.get(account_key) or "[]"
    account_infos = json.loads(account_list)
    for account_info in account_infos:
        await get_all_join_group(account_info, machine_ip)


if __name__ == '__main__':
    asyncio.run(repeat())
