# -*- coding: utf-8 -*-
# @Time : 2023/11/29 15:16
# @Site : 
# @File : kepler_interface.py
# @Software: PyCharm
import base64
import re
import json
import time
import httpx
import random
import asyncio
import datetime
from loguru import logger
from scrapy import Selector
from telethon import TelegramClient
from telethon.sessions import StringSession
from utils.crawl_tools import upload_file_to_oss
from utils.developer_infos import developer_list
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.types import InputPhoneContact, UserStatusOffline, UserStatusOnline, User
from telethon.tl.functions.contacts import ImportContactsRequest, DeleteContactsRequest
from utils.config import error_key, oss_base_path, save_url, redis_client, user_kafka_key

banned_key = "tg_spider:temp_banned"
account_key = "tg_spider:phone_accounts"
ipweb_url = "http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1"
ipweb_token = "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"

headers = {
    "authority": "t.me",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-language": "zh-CN,zh;q=0.9",
    "cache-control": "no-cache",
    "pragma": "no-cache",
    "sec-ch-ua": "\"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "none",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}


async def login(account_str):
    account_info = json.loads(account_str)
    phone = account_info.get("phone")
    client = None
    proxy_key = f"tg_spider:account_temp_proxy:{phone}"
    try:
        string_session = account_info.get("account_key")
        proxy = await get_proxy(proxy_key)
        developer_info = random.choice(developer_list)
        api_id = developer_info["api_id"]
        api_hash = developer_info["api_hash"]
        logger.info(f"账号【 {phone}】 开始登录,登录ip【 {proxy}】")
        client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=5, connection_retries=1)
        logger.info(f"start connect")
        await asyncio.wait_for(client.connect(), 3)
        is_user = await client.is_user_authorized()
        if not is_user:
            error_mess = f"手机号实时搜索账号【{phone}】登录失败"
            logger.error(error_mess)
            await redis_client.srem(account_key, account_str)
        else:
            logger.success(f"【{phone}】登录成功")
            return client
    except (Exception,) as e:
        logger.error(e)
        if client:
            await client.disconnect()
        error_mess = f"手机号实时搜索账号【{phone}】登录失败"
        await redis_client.sadd(error_key, error_mess)
        await redis_client.delete(proxy_key)


async def search_user_info_by_phone(phone):
    """
    :return:
    """
    while True:
        try:
            account_balance = await redis_client.scard(account_key)
            if account_balance < 80:
                error_mess = f"手机号实时搜索账号剩余数量低,剩余【{account_balance}】,请尽快补充账号"
                supplement = 80 - account_balance
                supplement_account = await redis_client.spop("tg_spider:account_pool", supplement)
                if supplement_account:
                    await redis_client.sadd(account_key, *supplement_account)
                await redis_client.sadd(error_key, error_mess)
            account_str = await redis_client.srandmember(account_key)
            account_info = json.loads(account_str)
            crawler_phone = account_info.get("phone")
            requests_num_key = "tg_spider:phone_search_requests_num:{}".format(crawler_phone)
            phone_total_key = "tg_spider:phone_total:{}".format(crawler_phone)
            cd_key = "tg_spider:phone_search_cd:{}".format(crawler_phone)
            # 获取是否在抓取cd里面
            cd_status = await redis_client.get(cd_key)
            # 获取请求次数
            number_of_requests = await redis_client.get(requests_num_key) or 0
            # 判断账号请求次数是否已经达到上限
            if int(number_of_requests) < 50 and not cd_status:
                client = await login(account_str)
                assert client
                try:
                    local_time = time.localtime(time.time())
                    year = local_time.tm_year
                    month = local_time.tm_mon
                    now_time = f"{year}-{month}"
                    logger.info(f"开始搜索{phone}的信息")
                    result = await client(ImportContactsRequest([InputPhoneContact(random.randrange(-2 ** 63, 2 ** 63), phone, ' ', ' ')]))
                    await redis_client.set(cd_key, 1, ex=int(10))
                    if result.retry_contacts:
                        logger.warning("账号添加好友过多，禁止添加")
                        # 将账号加入到临时封禁set并从账号池中删除,进行下一次搜索
                        await redis_client.sadd(banned_key, account_str)
                        await redis_client.srem(account_key, account_str)
                        continue
                    else:
                        # 请求次数计数
                        if int(number_of_requests) == 0:
                            await redis_client.set(requests_num_key, value="1", ex=24 * 60 * 60)
                        else:
                            await redis_client.incr(requests_num_key)
                        # 账号总请求次数计数
                        await redis_client.incr(phone_total_key)
                        # 开始获取用户信息
                        users = result.users
                        if users:
                            logger.info("用户手机号码:" + users[0].phone)
                            if users[0].username is not None:
                                logger.info("用户名称:" + users[0].username)
                            status = users[0].status
                            last_online_time = None
                            if isinstance(status, UserStatusOffline):
                                last_online_time = int(status.was_online.timestamp())
                                logger.info("用户最后在线时间" + status.was_online.strftime("%Y-%m-%d %R"))
                            elif isinstance(status, UserStatusOnline):
                                last_online_time = int(status.expires.timestamp())
                                logger.info("用户最后在线时间" + status.expires.strftime("%Y-%m-%d %R"))
                            photo = users[0].photo
                            user_id = users[0].id
                            user_avatar = None
                            if photo:
                                user_photo_byte = await client.download_profile_photo(entity=users[0], file=bytes, download_big=False)
                                oss_path = oss_base_path.format(f"/user/{now_time}/{user_id}.jpg")
                                upload_success = await upload_file_to_oss(oss_path, user_photo_byte)
                                if not upload_success:
                                    await redis_client.sadd(error_key, f"手机号【{phone}】头像上传失败")
                                user_avatar = save_url.format(oss_path)
                            await asyncio.sleep(2)
                            full = await client(GetFullUserRequest(users[0]))
                            about = full.full_user.about
                            del_result = await client(DeleteContactsRequest([users[0]]))
                            del_user = del_result.users
                            if del_user[0].first_name is not None:
                                logger.info("用户first_name:" + del_user[0].first_name)
                            if del_user[0].last_name is not None:
                                logger.info("用户last_name:" + del_user[0].last_name)
                            user_info = {
                                "avatar": user_avatar,
                                "firstName": del_user[0].first_name,
                                "lastName": del_user[0].last_name,
                                "lastOnline": last_online_time,
                                "userName": users[0].username,
                                "userPhone": users[0].phone,
                                "userId": users[0].id,
                                "message": True,
                                "about": about,
                            }
                            logger.info(user_info)
                            await redis_client.sadd(user_kafka_key, json.dumps({"Key": "user", "Body": user_info}, ensure_ascii=False))
                        else:
                            logger.warning(f"{phone},用户不存在")
                            user_info = {
                                "message": False,
                                "error": "用户不存在",
                                "userPhone": phone,
                            }
                    return user_info
                except (Exception,) as e:
                    logger.exception(e)
                    if "A wait of " in str(e):
                        wait_time = re.search("\d+", str(e)).group()
                        await redis_client.set(cd_key, 1, ex=int(wait_time))
                    continue
                finally:
                    await client.disconnect()
        except (Exception,) as e:
            logger.error(e)


async def search_user_info_by_username(user_link):
    while True:
        try:
            account_balance = await redis_client.scard(account_key)
            if account_balance < 80:
                # error_mess = f"手机号实时搜索账号剩余数量低,剩余【{account_balance}】,请尽快补充账号"
                # supplement = 80 - account_balance
                # supplement_account = await redis_client.spop("tg_spider:account_pool", supplement)
                # if supplement_account:
                #     await redis_client.sadd(account_key, *supplement_account)
                # await redis_client.sadd(error_key, error_mess)
                
                # 1. 创建一个基于当前小时的、用于速率限制的 Redis 键
                now = datetime.datetime.now()
                # 键的格式为 "error_limiter:年-月-日-小时"
                rate_limit_key = f"error_limiter:{now.strftime('%Y-%m-%d-%H')}"

                # 2. 检查当前小时的发送次数是否已达上限
                # redis.get 返回的是字节串，如果键不存在则返回 None，需要处理
                current_count_bytes = await redis_client.get(rate_limit_key)
                current_count = int(current_count_bytes) if current_count_bytes else 0

                if current_count < 5:
                    # --- 只有在未达到上限时，才执行以下操作 ---

                    # a. 增加计数器，并获取增加后的值
                    # INCR 是原子操作，能保证在并发时计数准确
                    new_count = await redis_client.incr(rate_limit_key)

                    # b. 如果这是本小时的第一次，为该键设置1小时 (3600秒) 的过期时间
                    # 这样 Redis 会在一小时后自动删除这个键，实现计数重置
                    if new_count == 1:
                        await redis_client.expire(rate_limit_key, 3600)
                        
                    # c. 创建并发送错误消息 (你原有的逻辑)
                    error_mess = f"手机号实时搜索账号剩余数量低,剩余【{account_balance}】,请尽快补充账号"
                    await redis_client.sadd(error_key, error_mess)

                # --- 账号补充逻辑不受影响，仍然执行 ---
                supplement = 80 - account_balance
                supplement_account = await redis_client.spop("tg_spider:account_pool", supplement)
                if supplement_account:
                    await redis_client.sadd(account_key, *supplement_account)

                
            account_str = await redis_client.srandmember(account_key)
            account_info = json.loads(account_str)
            crawler_phone = account_info.get("phone")
            requests_num_key = "tg_spider:username_search_requests_num:{}".format(crawler_phone)
            phone_total_key = "tg_spider:username_total:{}".format(crawler_phone)
            cd_key = "tg_spider:username_search_cd:{}".format(crawler_phone)
            # 获取是否在抓取cd里面
            cd_status = await redis_client.get(cd_key)
            # 获取请求次数
            number_of_requests = await redis_client.get(requests_num_key) or 0
            # 判断账号请求次数是否已经达到上限
            if int(number_of_requests) < 50 and not cd_status:
                client = await login(account_str)
                assert client
                try:
                    entity = await client.get_entity(user_link)
                    # 请求次数计数
                    if int(number_of_requests) == 0:
                        await redis_client.set(requests_num_key, value="1", ex=24 * 60 * 60)
                    else:
                        await redis_client.incr(requests_num_key)
                    # 账号总请求次数计数
                    await redis_client.incr(phone_total_key)
                    if isinstance(entity, User):
                        user_id = entity.id
                        status = entity.status
                        last_online_time = None
                        user_avatar = None
                        if isinstance(status, UserStatusOffline):
                            last_online_time = int(status.was_online.timestamp())
                            logger.info("用户最后在线时间" + status.was_online.strftime("%Y-%m-%d %R"))
                        elif isinstance(status, UserStatusOnline):
                            last_online_time = int(status.expires.timestamp())
                            logger.info("用户最后在线时间" + status.expires.strftime("%Y-%m-%d %R"))
                        now_time = time.strftime("%F", time.localtime())
                        user_photo_byte = await client.download_profile_photo(entity=entity, file=bytes, download_big=False)
                        oss_path = oss_base_path.format(f"/user/{now_time}/{user_id}.jpg")
                        upload_success = await upload_file_to_oss(oss_path, user_photo_byte)
                        if upload_success:
                            user_avatar = save_url.format(oss_path)
                        full = await client(GetFullUserRequest(entity))
                        about = full.full_user.about
                        user_json = entity.to_dict()
                        logger.info(user_json)
                        user_info = {
                            "avatar": user_avatar,
                            "firstName": user_json.get("first_name"),
                            "lastName": user_json.get("last_name"),
                            "lastOnline": last_online_time,
                            "userName": user_json.get("username"),
                            "userPhone": user_json.get("phone"),
                            "userId": user_id,
                            "message": True,
                            "about": about,
                        }
                        await redis_client.sadd(user_kafka_key, json.dumps({"Key": "user", "Body": user_info}, ensure_ascii=False))
                    else:
                        user_info = "输入的不是用户链接"
                    return user_info
                except (Exception,) as e:
                    logger.error(e)
                    if "A wait of " in str(e):
                        wait_time = re.search("\d+", str(e)).group()
                        await redis_client.set(cd_key, 1, ex=int(wait_time))
                    elif "No user has" in str(e) or "Nobody is using this username" in str(e) or "Cannot cast InputPeerUser " in str(e) or "The chat the user tried to join has expired" in str(e) or "Join the group and retry" in str(e):
                        user_info = "用户不存在"
                        return user_info
                finally:
                    await client.disconnect()
        except (Exception,) as e:
            logger.error(e)


async def get_proxy(proxy_key, max_number=10):
    proxy_str = await redis_client.get(proxy_key)
    if proxy_str:
        proxy = json.loads(proxy_str)
        return proxy
    while max_number > 0:
        try:
            await asyncio.sleep(0.5)
            resp = httpx.get("http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1", headers={"Token": "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            proxy = {
                'proxy_type': 'http',
                'addr': "gate1.ipweb.cc",
                'port': 7778,
                'username': username,
                'password': password,
                'rdns': True
            }
            await redis_client.set(proxy_key, json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
            return proxy
        except (Exception,) as e:
            max_number -= 1
            logger.error(e)
            await asyncio.sleep(1)


async def get_httpx_proxy(max_number=10):
    while max_number > 0:
        try:
            resp = httpx.get(ipweb_url, headers={"Token": ipweb_token})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            return {'http://': f'http://{username}:{password}@gate1.ipweb.cc:7778', 'https://': f'http://{username}:{password}@gate1.ipweb.cc:7778'}
        except (Exception,) as e:
            max_number -= 1
            time.sleep(1)


async def search_link_info(url):
    resp = await send_requests(url)
    html = Selector(text=resp.text)
    page_extra = "".join(html.xpath('//div[@class="tgme_page_extra"]//text()').getall())
    member_count = 0
    if page_extra:
        if "members" in page_extra:
            category = "group"
            member_count = re.sub("\s+", "", page_extra.split("members")[0])
        elif "subscriber" in page_extra:
            category = "channel"
            member_count = re.sub("\s+", "", page_extra.split("subscriber")[0])
        else:
            category = "user"
    else:
        category = ""
    logger.info(f"【{url}】的类型是：{category}")
    name = "".join(html.xpath('//div[@class="tgme_page_title"]//text()').getall()).strip()
    desc = "".join(html.xpath('//div[@class="tgme_page_description"]//text()').getall()).strip()
    cover_img = html.xpath('//img[@class="tgme_page_photo_image"]/@src').get()
    group_info = {
        "name": name,
        "desc": desc,
        "link": url,
        "memberCount": member_count,
        "category": category,
        "coverImg": cover_img,
    }
    if category in ["group", "channel"]:
        await redis_client.sadd("tg_spider:crawl:subscription_links", url)
    return group_info


async def send_requests(url, max_num=5, **kwargs):
    client = httpx.AsyncClient()
    while max_num > 0:
        try:
            await asyncio.sleep(0.5)
            resp = await client.get(url, headers=headers, **kwargs, timeout=10)
            if resp.status_code == 200:
                return resp
            else:
                max_num -= 1
        except (Exception,) as e:
            logger.error(e)
            await asyncio.sleep(1)
