# -*- coding: utf-8 -*-
import re
import csv
import json
import time

import redis
import requests
import pandas as pd
from math import ceil
from lxml.etree import HTML
from utils.config import redis_host, redis_port, redis_password


class loadFile(object):
    def __init__(self):
        pool = redis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding="utf-8", decode_responses=True, health_check_interval=30)
        self.redis_client = redis.Redis(connection_pool=pool)
        self.updated_daily_task_key = "tg_spider:updated_daily"
        self.monthly_daily_task_key = "tg_spider:monthly_daily"
        self.account_key = "tg_spider:account:{}"
        self.priority_key = "tg_spider:priority:{}"
        self.phone_key = "tg_spider:phone"
        self.group_info_key = "tg_spider:group_info:{}"
        self.offer_key = "tg_spider:max_offset:{}"

    def load_identification(self):
        account_redis_client = redis.from_url("redis://{}".format('r-bp18466abkgg3jyebipd.redis.rds.aliyuncs.com'), port=6379, password="noob@2023", encoding="utf-8", decode_responses=True, health_check_interval=30)
        df = pd.read_csv("./file/ext_links_String.csv")
        print(df.shape[0])
        df['ext_links'] = df['ext_links'].apply(lambda x: json.loads(x))
        links = df["ext_links"].values.tolist()
        flat_list = list(set([self.tg_link_extract(item) for sublist in links for item in sublist]))
        flat_list.remove("")
        new_list = self.list_slicing(flat_list, 100000)
        print(len(new_list))
        for item in new_list:
            account_redis_client.sadd("tg_spider:identification:task", *item)

    def load_link(self):
        df = pd.read_excel("./file/data.xls")
        df.fillna("", inplace=True)
        for i in df.index.values:
            df_dict = df.loc[i, ["社群/频道名称", "链接", "群简介"]].to_dict()
            text = df_dict.get("社群/频道名称", "") + df_dict.get("群简介", "")
            top_priority_words = ["法轮功", "唐人", "大法", "枪", "小粉红", "共产党", "习近平", "习包子", "真相", "肉铺", "道友", "冰毒", "叶子", "大麻"]
            middle_priority_words = ["水军", "社工", "查档", "担保", "一道", "精聊", "跑分"]
            link = df_dict["链接"]
            if self.redis_client.sismember("tg_spider:links_no_user", link) or self.redis_client.sismember("tg_spider:verification", link):
                continue
            if not self.redis_client.sismember("tg_spider:group_info_crawl_success", link):
                self.redis_client.sadd("tg_spider:group_info_links", link)
            if self.priority_check(text, top_priority_words):
                self.redis_client.sadd(self.priority_key.format("top"), link)
            elif self.priority_check(text, middle_priority_words):
                self.redis_client.sadd(self.priority_key.format("middle"), link)
                self.redis_client.sadd(self.updated_daily_task_key, link)
            else:
                self.redis_client.sadd(self.priority_key.format("low"), link)
                self.redis_client.sadd(self.monthly_daily_task_key, link)

    def load_phone(self):
        import csv
        with open('file/152wphone.csv', 'r') as file:
            reader = csv.reader(file)
            for row in reader:
                if self.redis_client.sismember("tg_spider:phone_succ", row[0]):
                    continue
                self.redis_client.sadd(self.phone_key, row[0])

    def load_tg_link_csv(self):
        import csv
        with open('file/tg_link_5w.csv', 'r', encoding="utf-8") as file:
            reader = csv.reader(file)
            for row in reader:
                url = row[0]
                if url:
                    self.redis_client.sadd("tg_spider:identification:task", url)

    def tg_link(self):
        tg_links = []
        with open("./file/link.txt", 'r', encoding="utf-8") as file:
            for line in file.readlines():
                tg_links.append(line.strip())
        # tg_links =
        self.redis_client.sadd("tg_spider:crawl:history:task_links", *tg_links)

    def load_search(self):
        df = pd.read_csv('file/search_group_link_String.csv')
        records = df[df["member_count"] > 10000].to_dict('records')
        self.redis_client.sadd("tg_spider:search_group_link_wait", *[json.dumps(_, ensure_ascii=False) for _ in records])

    def load_group_max_id(self):
        import csv
        with open('file/max_id.csv', 'r') as file:
            reader = csv.reader(file)
            reader.__next__()
            for row in reader:
                group_id = row[0]
                max_id = row[1]
                self.redis_client.hset("tg_spider:max_offset", group_id, max_id)

    def load_supplementary(self):
        import csv
        with open("./file/supplementary.csv", 'r') as file:
            reader = csv.reader(file)
            for row in reader:
                link = row[1]
                min_id = row[2]
                max_id = row[3]
                self.redis_client.sadd("tg_spider:missing_data", json.dumps({"link": link, "min_id": min_id, "max_id": max_id}))

    @staticmethod
    def list_slicing(data_list: list, num=100):
        """
        :param data_list: 需要切割的list
        :param num: 需要多少元素一个list,默认100个一个list
        :return:
        """
        new_list = []
        for index in range(ceil(len(data_list) / num)):
            new_list.append(data_list[index * num:(index + 1) * num])
        return new_list

    @staticmethod
    def priority_check(source_data, field_list):
        for field in field_list:
            if field in source_data:
                return True
        return False

    @staticmethod
    def tg_link_extract(link):
        rule_one = re.search("https://t\.me/[^/?#]{1,100}", link)
        if rule_one:
            return rule_one.group().replace("@", "").rstrip("https:")

    def bind_ip(self):
        import csv
        with open("file/ips", "r", encoding="utf-8") as f:
            ips = f.readlines()
        with open("file/tg-accounts.csv", "r") as file:
            reader = list(csv.DictReader(file))
            step = 1
            pop_index = 0
            for index, session_info in enumerate(reader):
                if index == pop_index:
                    ip = ips.pop()
                    account_key = self.account_key.format(ip.strip())
                    values = json.dumps(reader[index:index + step], ensure_ascii=False)
                    self.redis_client.set(account_key, values)
                    pop_index = index + step
                if pop_index < 30:
                    step = 1
                elif pop_index < 60:
                    step = 2
                elif pop_index < 90:
                    step = 3
                else:
                    step = 4

    def fill_promotion_task(self):
        df = pd.read_csv("./file/promotion_target.csv")
        groups = df["link"].to_list()
        self.redis_client.sadd("tg_spider:promotion:task", *groups)

    def fill_promotion_task2(self):
        with open("./file/mengge.txt", 'r') as file:
            for line in file:
                self.redis_client.sadd("tg_spider:special_task", line.strip())
                self.redis_client.sadd("tg_spider:promotion:task", line.strip())

    def temp_load(self):
        import csv
        with open("./file/tgc_tg_channel_sender_all.csv", 'r') as file:
            reader = csv.reader(file)
            for row in reader:
                phone = row[0]
                if len(phone) == 11:
                    self.redis_client.sadd("tg_spider:phone_search:task", "86" + phone)


if __name__ == '__main__':
    load_file = loadFile()
    load_file.temp_load()
    # load_file.links()
    # load_file.load_account_files()
    # load_file.load_send_objectives()
    # load_file.load_text()
    # load_file.load_send_objectives()
    # load_file.load_group_max_id()
    # load_file.load_identification()
    # load_file.load_csv()
    # load_file.fill_promotion_task()
    # load_file.fill_promotion_task2()
