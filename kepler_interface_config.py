"""gunicorn+uvicorn 的配置文件"""

# 绑定 ip + 端口
bind = "0.0.0.0:10086"
# 进程数只要一个进程
workers = 1

# 线程数 = cup数量 * 2
threads = 4

# 等待队列最大长度,超过这个长度的链接将被拒绝连接
backlog = 2048

# 工作模式--uvicorn
worker_class = "uvicorn.workers.UvicornWorker"

# 最大客户客户端并发数量,对使用线程和协程的worker的工作有影响
# 服务器配置设置的值  1200：中小型项目  上万并发： 中大型
# 服务器硬件：宽带+数据库+内存
# 服务器的架构：集群 主从
worker_connections = 1200

# 进程名称
proc_name = 'kepler_interface.pid'
# 进程pid记录文件
pidfile = '../logs/kepler_interface_fastapi.log'
# 日志等级
loglevel = 'info'
# 日志文件名
logfile = '../logs/kepler_interface_fastapi_info.log'
# 访问记录
accesslog = '../logs/kepler_interface_fastapi_access.log'
# 访问记录格式
access_log_format = '%(h)s %(t)s %(U)s %(q)s'
