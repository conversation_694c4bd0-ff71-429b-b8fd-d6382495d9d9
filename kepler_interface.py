# -*- coding: utf-8 -*-
# @Time : 2023/11/29 15:16
# @Site : 
# @File : kepler_interface.py
# @Software: PyCharm
import json
import uuid
from typing import Optional
from gmssl import sm3, sm4
from fastapi_limiter import FastAPILimiter
from fastapi import Fast<PERSON><PERSON>, Depends, Header, Request
from fastapi_limiter.depends import RateLimiter
from starlette.middleware.gzip import GZipMiddleware
from utils.config import log_path
from kepler_interface_tools import *

app = FastAPI()
app.add_middleware(GZipMiddleware, minimum_size=1000)

logger.add(
    sink="%s_{time:YYYY_MM_DD}.log" % (log_path / "interface"),
    encoding="utf8",
    format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
    rotation="00:00",
    retention="1 days"
)
sm4_key = "f795e34c94384805".encode()


async def check_token(x_timestamp, x_token):
    body = "4df4b30c34205a41517ad6208ff5b181" + x_timestamp
    # 通过sms3计算出 加密和解密使用需要使用的key
    msg_list = [i for i in bytes(body.encode('UTF-8'))]
    token = sm3.sm3_hash(msg_list)
    return token == x_token


async def sm4_encryption(text):
    crypt_sm4 = sm4.CryptSM4()
    crypt_sm4.set_key(sm4_key, sm4.SM4_ENCRYPT)
    data = b""
    if isinstance(text, str):
        data = text.encode()
    elif isinstance(text, dict):
        data = json.dumps(text).encode()
    encrypt_value = crypt_sm4.crypt_ecb(data)
    return base64.b64encode(encrypt_value).decode()


async def sm4_decryption(text):
    crypt_sm4 = sm4.CryptSM4()
    data = base64.b64decode(text)
    crypt_sm4.set_key(sm4_key, sm4.SM4_DECRYPT)
    decrypt_value = crypt_sm4.crypt_ecb(data)
    return decrypt_value.decode()


@app.post('/v2/phoneSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def phone_search(requests: Request, x_timestamp: Optional[str] = Header(None), x_token: Optional[str] = Header(None)):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        if await check_token(x_timestamp, x_token):
            encrypted_body = await requests.body()
            body_ = await sm4_decryption(encrypted_body)
            reqs_body = json.loads(body_)
            phone = reqs_body.get("phone")
            user_info = await search_user_info_by_phone(phone)
            results["code"] = 200
            results["message"] = "成功"
            results["data"] = await sm4_encryption(user_info)
            logger.success(f"手机号搜索任务结果: {results}")
        else:
            results["code"] = 401
            results["message"] = "鉴权不通过"
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"手机号搜索任务结果: {results}")
    finally:
        return results


@app.post('/v2/usernameSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def username_search(requests: Request, x_timestamp: Optional[str] = Header(None), x_token: Optional[str] = Header(None)):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        if await check_token(x_timestamp, x_token):
            encrypted_body = await requests.body()
            body_ = await sm4_decryption(encrypted_body)
            reqs_body = json.loads(body_)
            username = reqs_body.get("username")
            if str(username).startswith("https://t.me/") or str(username).startswith("http") is False:
                if str(username).startswith("https://t.me/"):
                    user_link = username
                else:
                    user_link = f"https://t.me/{username}"
                user_info = await search_user_info_by_username(user_link)
                if isinstance(user_info, dict):
                    results["message"] = "成功"
                    results["data"] = await sm4_encryption(user_info)
                results["code"] = 200
                logger.success(f"用户名搜索任务结果: {results}")
            elif str(username).startswith("http"):
                results["code"] = 200
                results["data"] = await sm4_encryption("输入的链接错误")
        else:
            results["code"] = 401
            results["message"] = "鉴权不通过"
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"用户名搜索任务结果: {results}")
    finally:
        return results


@app.post('/v2/linkSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def link_search(requests: Request, x_timestamp: Optional[str] = Header(None), x_token: Optional[str] = Header(None)):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        logger.info(x_timestamp)
        logger.info(x_token)
        if await check_token(x_timestamp, x_token):
            encrypted_body = await requests.body()
            body_ = await sm4_decryption(encrypted_body)
            reqs_body = json.loads(body_)
            url = reqs_body.get("link")
            link_info = await search_link_info(url)
            category = link_info.get("category")
            if category in ["group", "channel", "user"]:
                code = 200
                results["data"] = await sm4_encryption(link_info)
            else:
                code = 200
            results["message"] = "成功"
            results["code"] = code
            logger.success(f"链接搜索任务结果: {results}")
        else:
            results["code"] = 401
            results["message"] = "鉴权不通过"
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"链接搜索任务结果: {results}")
    finally:
        return results


@app.post('/v2/queryGeoLocatorTaskResult', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def query_geo_locator_results(requests: Request, x_timestamp: Optional[str] = Header(None), x_token: Optional[str] = Header(None)):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        if await check_token(x_timestamp, x_token):
            encrypted_body = await requests.body()
            body_ = await sm4_decryption(encrypted_body)
            reqs_body = json.loads(body_)
            task_id = reqs_body.get("taskId")
            task_results = await redis_client.get(f"tg_spider:geo_results:{task_id}")
            if task_results:
                try:
                    data = json.loads(task_results)
                    if len(data.get("users")) > 0 or len(data.get("groups")) > 0:
                        data.update({
                            "taskId": task_id,
                            "status": "completed",
                            "statusMsg": "分析完成"
                        })
                    else:
                        data.update({
                            "taskId": task_id,
                            "status": "no_data",
                            "statusMsg": "无数据"
                        })
                except:
                    data = {
                        "taskId": task_id,
                        "status": "failed",
                        "statusMsg": task_results
                    }
                results["code"] = 200
                results["message"] = "成功"
                results["data"] = await sm4_encryption(data)
            else:
                data = {
                    "taskId": task_id,
                    "status": "analyzing",
                    "statusMsg": "分析中"
                }
                results["code"] = 200
                results["message"] = "成功"
                results["data"] = await sm4_encryption(data)
            logger.success(f"附件的群组和人搜索任务结果: {results}")
        else:
            results["code"] = 401
            results["message"] = "鉴权不通过"
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"附件的群组和人搜索任务结果: {results}")
    finally:
        return results


@app.post('/phoneSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def phone_search(phone: dict):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        phone = phone.get("phone")
        user_info = await search_user_info_by_phone(phone)
        results["code"] = 200
        results["message"] = "成功"
        results["data"] = user_info
        logger.success(f"手机号搜索任务结果: {results}")
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"手机号搜索任务结果: {results}")
    finally:
        return results


@app.post('/usernameSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def username_search(user: dict):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        username = user.get("username")
        if str(username).startswith("https://t.me/") or str(username).startswith("http") is False:
            if str(username).startswith("https://t.me/"):
                user_link = username
            else:
                user_link = f"https://t.me/{username}"
            user_info = await search_user_info_by_username(user_link)
            if isinstance(user_info, dict):
                results["message"] = "成功"
                results["data"] = user_info
            results["code"] = 200
            logger.success(f"用户名搜索任务结果: {results}")
        elif str(username).startswith("http"):
            results["code"] = 200
            results["data"] = "输入的链接错误"
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"用户名搜索任务结果: {results}")
    finally:
        return results


@app.post('/linkSearch', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def link_search(group: dict):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        logger.info(group)
        url = group.get("link")
        link_info = await search_link_info(url)
        category = link_info.get("category")
        if category in ["group", "channel", "user"]:
            code = 200
            results["data"] = link_info
        else:
            code = 200
        results["message"] = "成功"
        results["code"] = code
        logger.success(f"链接搜索任务结果: {results}")
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"链接搜索任务结果: {results}")
    finally:
        return results


@app.post('/queryGeoLocatorTaskResult', dependencies=[Depends(RateLimiter(times=10, seconds=1))])
async def query_geo_locator_results(geo_info: dict):
    request_id = uuid.uuid4().hex
    logger.info(f"requestId:{request_id}")
    results = {"requestId": request_id}
    try:
        task_id = geo_info.get("taskId")
        task_results = await redis_client.get(f"tg_spider:geo_results:{task_id}")
        if task_results:
            try:
                data = json.loads(task_results)
                if len(data.get("users")) > 0 or len(data.get("groups")) > 0:
                    data.update({
                        "taskId": task_id,
                        "status": "completed",
                        "statusMsg": "分析完成"
                    })
                else:
                    data.update({
                        "taskId": task_id,
                        "status": "no_data",
                        "statusMsg": "无数据"
                    })
            except:
                data = {
                    "taskId": task_id,
                    "status": "failed",
                    "statusMsg": task_results
                }
            results["code"] = 200
            results["message"] = "成功"
            results["data"] = data
        else:
            data = {
                "taskId": task_id,
                "status": "analyzing",
                "statusMsg": "分析中"
            }
            results["code"] = 200
            results["message"] = "成功"
            results["data"] = data
        logger.success(f"附件的群组和人搜索任务结果: {results}")
    except (Exception,) as e:
        logger.exception(e)
        results["code"] = 500
        results["message"] = "服务内部错误"
        logger.warning(f"附件的群组和人搜索任务结果: {results}")
    finally:
        return results


@app.on_event("startup")
async def startup_event():
    app.state.redis = redis_client
    await FastAPILimiter.init(app.state.redis)
    logger.info(f"redis连接完成--->>{app.state.redis}")


if __name__ == '__main__':
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=10086)
