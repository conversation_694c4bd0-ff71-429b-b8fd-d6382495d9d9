import re
import time
import json
import httpx
import random
import asyncio
import platform
from loguru import logger
from telethon import types
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.tl.functions import contacts
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.functions.contacts import ImportContactsRequest, DeleteContactsRequest
from telethon.tl.types import User, UserStatusOffline, UserStatusOnline, InputPhoneContact, Chat
from telethon.tl.functions.messages import GetPeerDialogsRequest, ImportChatInviteRequest, GetUnreadMentionsRequest

from utils.developer_infos import developer_list
from utils.crawl_tools import get_group_info, get_history_msg, upload_file_to_oss
from utils.config import redis_client, oss_base_path, save_url, user_kafka_key, error_key, log_path

logger.add(
    sink="%s_{time:YYYY_MM_DD}.log" % (log_path / "local_test"),
    encoding="utf8",
    format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
    rotation="00:00",
    retention="1 days"
)
os_name = platform.system()
if os_name.lower() == 'windows':
    proxy = {
        'proxy_type': 'http',
        'addr': "localhost",
        'port': 10809,
        'rdns': True
    }
    account_info = {
        'phone': '*************',
        'account_key': '1BVtsOKoBu5vewbR5qXOU-TYwOTkpUi3OfG_SNoTSviHwT0TTCj2MmoQoxSiJY9UqAbVSPK1D6siV8I-7_U21nkur0PAozbJZpRAfrp74LKgiyPt4tw_FRjyok35leZhemZRa3ELfDHzIqvU6_kg3b392LPZBQ3nkUeIAKP1YWEitrbsHwhG5gsVBY9PxuKnt9aFyiSTnegGglp45H5UMuW2HOufylENRULQohafjF2OyLR4WG36i1BtScTwusg4DBIl84MtaS7wu8ta4xwRZd4YexDJ6EHE6dYM9oyt7Y_Bf3sQvJ0pnwaDBMPKPzVdtUJMc8mUJO_E7EweWvZxso4hxxmqapQg=',
        'api_id': '********',
        'api_hash': '5b390a6f08fe90db8428c976f2797d13'
    }

else:
    proxy = {}
    account_info = {
        "phone": "************",
        "account_key": "1BJWap1wBuxlr6saIs2jWlz3oB8Bo5Mm41K4WaH6mfE6LMnibslGu69T99iz_0Pz9QtQmkwyN_CzeLz49qxtISAFyXILaQzEWZP7AqVLvd4c3r_w0MN9dc-Vy5HfKUOFBjU3YMEOwFVjVAahrh0rAm2JM3guCEu3A_ydCL70ISHbL8CAYeSHptDbqAaMvwyCHjUWWffrYoSv4qJrjc6KoSM2oagvjKpwS2qsXOxPVTSWH9ttwx6xzxnwin3pnXgPZaLrRITVxhArL3bjnBnpu1_arbAFMNPqMEkjg3dn4T7xdpOp58VzhH4PvfLf7s70i1pIPVTF6-R9l0vsOTX-1A3u75QaBEGQ=",
        "email_account": "<EMAIL>",
        "api_id": "********",
        "api_hash": "b00299398965f1d12eb8a40beba48f83"
    }

string_session = account_info["account_key"]


async def login():
    developer_info = random.choice(developer_list)
    api_id = developer_info["api_id"]
    api_hash = developer_info["api_hash"]
    print(proxy)
    client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
    await client.connect()
    is_user = await client.is_user_authorized()
    if not is_user:
        print(">>>>>>>>>>>>>>>>>>>>登录失败")
        client.disconnect()
    else:
        print("success")
        return client


async def get_proxy(max_number=10):
    while max_number > 0:
        try:
            resp = httpx.get("http://api.ipweb.cc:8004/api/agent/account2?country=US&times=90&limit=1",
                             headers={"Token": "5D6GDK2CMLDXJQPHS4QV0N9QWLU1ZB5O"})
            username = resp.json()["data"][0].split(":")[0]
            password = resp.json()["data"][0].split(":")[1]
            proxy = {
                'proxy_type': 'http',  # (mandatory) protocol to use (see above)
                'addr': "gate1.ipweb.cc",  # (mandatory) proxy IP address
                'port': 7778,  # (mandatory) proxy port number
                'username': username,  # (optional) username if the proxy requires auth
                'password': password,  # (optional) password if the proxy requires auth
                'rdns': True  # (optional) whether to use remote or local resolve, default remote
            }
            return proxy
        except (Exception,) as e:
            max_number -= 1
            await asyncio.sleep(1)


def get_proxy_from_ipidea(max_number=5):
    while max_number > 0:
        try:
            url = "http://api.proxy.ipidea.io/getProxyIp?num=1&tag=static_us_balance&return_type=json&lb=1&sb=0&flow=1&protocol=http&sesstime=120"
            resp = httpx.get(url)
            proxy_json = resp.json()["data"][0]
            proxy = {
                'proxy_type': 'http',  # (mandatory) protocol to use (see above)
                'addr': proxy_json["ip"],  # (mandatory) proxy IP address
                'port': proxy_json["port"],  # (mandatory) proxy port number
                'rdns': True  # (optional) whether to use remote or local resolve, default remote
            }
            return proxy
        except (Exception,) as e:
            max_number -= 1
            logger.error(e)
            time.sleep(1)


async def get_geo_info():
    client = await login()
    result = await client(contacts.GetLocatedRequest(
        geo_point=types.InputGeoPoint(lat=22.4963479, long=113.9168192, accuracy_radius=100),
    ))
    print(result.to_json())
    # distance_dict = {_.get("peer").get("user_id", _.get("peer").get("channel_id")): _.get("distance") for _ in result.updates[0].to_dict().get("peers")}
    for chat in result.chats:
        try:
            time.sleep(1)
            photo_bytes = await client.download_profile_photo(entity=chat, file=bytes)
            print(photo_bytes)
        except Exception as e:
            print(e)
    await client.disconnect()


async def crawl_all_msg():
    client = await login()
    links = [
        "https://t.me/KaLi_Club_China"
    ]
    for link in links:
        try:
            if "joinchat" in link or "+" in link:
                if "joinchat" in link:
                    hash_str = link.replace("https://t.me/joinchat/", "")
                else:
                    hash_str = re.search("(?<=\+).*", link).group()
                try:
                    join_status = await client(ImportChatInviteRequest(hash_str))
                except(Exception) as e:
                    print(e)
            entity = await client.get_entity(link)
            channel_name = entity.title
            logger.info(f"开始采集历史信息:【{channel_name}】,link: 【{link}】")
            # # 采集群组基础信息
            offset = 0
            msg_total = 0
            group_info = await get_group_info(client, link, entity)
            logger.info(group_info)
            # while True:
            #     try:
            #         msgs = await get_history_msg(client, 1000, link, int(offset), False, entity)
            #         msg_len = len(msgs)
            #         if msg_len > 0:
            #             offset = msgs[-1].get("msgId", 0)
            #             msg_total += msg_len
            #             print(offset)
            #         elif msg_len < 990:
            #             break
            #     except (Exception,) as e:
            #         logger.exception(e)
            logger.info(f"【{link}】历史信息采集完毕 共采集了【{msg_total}】条消息")
        except (Exception,) as e:
            logger.exception(e)
    await client.disconnect()


async def search_user_info_by_phone():
    client = await login()

    task_phones = [
        "************",
    ]
    crawler_phone = account_info["phone"]
    now_time = time.strftime("%F", time.localtime())
    for task_phone in task_phones:
        result = await client(ImportContactsRequest([InputPhoneContact(random.randrange(-2 ** 63, 2 ** 63), task_phone, ' ', ' ')]))
        retry_contacts = result.retry_contacts
        if retry_contacts:
            logger.warning(result.to_dict())
            logger.warning(retry_contacts)
            logger.warning(f"【{crawler_phone}】添加好友过多，禁止添加")
            continue
        else:
            # 请求计数
            users = result.users
            if users:
                logger.info("用户手机号码:" + users[0].phone)
                if users[0].username is not None:
                    logger.info("用户名称:" + users[0].username)
                status = users[0].status
                last_online_time = None
                if isinstance(status, UserStatusOffline):
                    last_online_time = int(status.was_online.timestamp())
                    logger.info("用户最后在线时间" + status.was_online.strftime("%Y-%m-%d %R"))
                elif isinstance(status, UserStatusOnline):
                    last_online_time = int(status.expires.timestamp())
                    logger.info("用户最后在线时间" + status.expires.strftime("%Y-%m-%d %R"))
                photo = users[0].photo
                user_id = users[0].id
                user_avatar = None
                if photo:
                    user_photo_byte = await client.download_profile_photo(entity=users[0], file=bytes, download_big=False)
                    oss_path = oss_base_path.format(f"/user/{now_time}/{user_id}.jpg")
                    upload_success = await upload_file_to_oss(oss_path, user_photo_byte)
                    if not upload_success:
                        await redis_client.sadd(error_key, f"手机号【{task_phone}】头像上传失败")
                    user_avatar = save_url.format(oss_path)
                await asyncio.sleep(1)
                full = await client(GetFullUserRequest(users[0]))
                about = full.full_user.about
                del_result = await client(DeleteContactsRequest([task_phone]))
                del_user = del_result.users
                if del_user[0].first_name is not None:
                    logger.info("用户first_name:" + del_user[0].first_name)
                if del_user[0].last_name is not None:
                    logger.info("用户last_name:" + del_user[0].last_name)
                user_info = {
                    "avatar": user_avatar,
                    "firstName": del_user[0].first_name,
                    "lastName": del_user[0].last_name,
                    "lastOnline": last_online_time,
                    "userName": users[0].username,
                    "userPhone": users[0].phone,
                    "userId": users[0].id,
                    "message": True,
                    "about": about,
                    "crawlerPhone": crawler_phone
                }
                await redis_client.sadd("tg_spider:phone_search:succ", task_phone)
                await redis_client.sadd(user_kafka_key, json.dumps({"Key": "user", "Body": user_info}, ensure_ascii=False))
            else:
                logger.warning(f"{task_phone},用户不存在")
                user_info = {
                    "message": False,
                    "error": "用户不存在",
                    "userPhone": task_phone,
                    "crawlerPhone": crawler_phone
                }
        logger.info(user_info)
        await asyncio.sleep(10)
        await redis_client.sadd("tg_spider:phone_search:results", json.dumps(user_info, ensure_ascii=False))


async def account_status_check():
    while True:
        # 随机登录一个账号
        client = await login()
        if not client:
            continue
        try:
            # 开始判断账号状态
            user_link = "https://t.me/SpamBot"
            entity = await client.get_entity(user_link)
            async with client.conversation(entity, timeout=10) as conv:
                await conv.send_message("/start")
                while True:
                    await asyncio.sleep(3)
                    response = await conv.get_response()
                    messages = response.message
                    print(messages)
                await conv.cancel_all()
        except (Exception,) as e:
            logger.error(e)
        finally:
            await client.disconnect()


async def get_user_info_by_username():
    client = await login()

    user_name = "dachuan88"
    user_link = f"https://t.me/{user_name}"
    entity = await client.get_entity(user_link)
    user_id = entity.id
    status = entity.status
    last_online_time = None
    user_avatar = None
    if isinstance(status, UserStatusOffline):
        last_online_time = int(status.was_online.timestamp())
        logger.info("用户最后在线时间" + status.was_online.strftime("%Y-%m-%d %R"))
    elif isinstance(status, UserStatusOnline):
        last_online_time = int(status.expires.timestamp())
        logger.info("用户最后在线时间" + status.expires.strftime("%Y-%m-%d %R"))
    now_time = time.strftime("%F", time.localtime())
    user_photo_byte = await client.download_profile_photo(entity=entity, file=bytes, download_big=False)
    oss_path = oss_base_path.format(f"/user/{now_time}/{user_id}.jpg")
    # upload_success = await upload_file_to_oss(oss_path, user_photo_byte)
    # if upload_success:
    #     user_avatar = save_url.format(oss_path)
    full = await client(GetFullUserRequest(entity))
    about = full.full_user.about
    user_info = {
        "avatar": user_avatar,
        "firstName": entity.first_name,
        "lastName": entity.last_name,
        "lastOnline": last_online_time,
        "userName": entity.username,
        "userPhone": entity.phone,
        "userId": entity.id,
        "message": True,
        "about": about,
    }
    print(user_info)
    await client.disconnect()
    # await redis_client.sadd(user_kafka_key, json.dumps({"Key": "user", "Body": user_info}, ensure_ascii=False))


async def test_iter_download():
    client = await login()
    dialogs = await client.get_dialogs()
    channel_dialogs = [dialog for dialog in dialogs if dialog.is_channel]
    for dialogs in channel_dialogs:
        unread_count = dialogs.unread_count
        peer = await client(GetPeerDialogsRequest([dialogs]))
        min_id = peer.dialogs[0].read_inbox_max_id
        max_id = peer.dialogs[0].read_outbox_max_id
        message = await client(GetUnreadMentionsRequest(dialogs.entity, min_id, 0, unread_count, max_id, min_id))
        print(message)


async def join_group():
    """
    加群
    :return:
    """
    client = await login()

    while True:
        group_link = "https://t.me/+VITQjreuPjU0ZTQ1"
        try:
            if "joinchat" in group_link or "+" in group_link:
                if "joinchat" in group_link:
                    hash_str = group_link.replace("https://t.me/joinchat/", "")
                else:
                    hash_str = re.search("(?<=\+).*", group_link).group()
                join_status = await client(ImportChatInviteRequest(hash_str))
            entity = await client.get_entity(group_link)
            group_id = entity.id
            # 避免重复加群
            if isinstance(entity, User):
                continue
            # 这些群组的附件需要存储,需要单独记录到一个key里面,用于download_media方法里面判断使用
            await get_group_info(client, group_link, entity)
            await redis_client.sadd("tg_spider:storage_group", group_id)
        except (Exception,) as e:
            logger.error(e)
        finally:
            await client.disconnect()


async def get_all_join_group():
    """获取账号加入的所有群信息"""
    phone = account_info.get("phone")
    group_join_info = []
    client = await login()
    dialogs = await client.get_dialogs()
    for dialog in dialogs:
        channel_id = dialog.entity.id
        if dialog.is_user:
            continue
        else:
            if hasattr(dialog.entity, "username") and dialog.entity.username:
                user_name = dialog.entity.username
                channel = f"https://t.me/{user_name}"
            else:
                channel = await redis_client.hget("tg_spider:group_info", channel_id)
                entity = await client.get_entity(dialog.entity)
                if isinstance(entity, Chat):
                    continue
                    # entity = await client(GetFullChatRequest(dialog.entity))
                print(entity.to_json())
                # await get_group_info(client, channel, dialog.entity)
            # await redis_client.sadd("tg_spider:group_join_succ_already", channel_id)
            group_join_info.append({"id": dialog.entity.id, "link": channel})
    await redis_client.hset("tg_spider:group_info", phone, json.dumps(group_join_info, ensure_ascii=False))
    logger.info(f"【{phone}】 共加入 【{len(group_join_info)}】个群")
    return group_join_info


def test():
    with open("file/exists_link") as f:
        exists_link = f.readlines()
    with open("file/links") as f:
        all_link = f.readlines()
    diff_list = [_.strip() for _ in list(set(all_link) - set(exists_link))]
    print(diff_list)


if __name__ == '__main__':
    # print(get_proxy_from_ipidea())
    # asyncio.run(login())
    print(asyncio.run(search_user_info_by_phone()))
    # test()
