import os
import sys
import oss2
import yaml
import aioredis
from pathlib import Path
from loguru import logger

sys.path.append(os.path.join(os.getcwd()))

"""
读取配置文件模块
"""
with open(str(Path(__file__).parent.parent / 'config.yml')) as cfg_f:
    config = yaml.safe_load(cfg_f.read())
    environment = config['environment']
    logger.debug(f"Load config file successful. environment: {environment}")

# tg 配置
tg_api_cfg = config["tg_api"]
api_id = tg_api_cfg["api_id"]
api_hash = tg_api_cfg["api_hash"]

# 日志配置
log_path = Path(__file__).parent.parent.parent / 'logs'
if not os.path.exists(log_path):
    os.mkdir(log_path)

# redis配置
redis_cfg = config["redis"]
redis_host = redis_cfg["host"]
redis_port = redis_cfg["port"]
redis_password = redis_cfg["password"]
# 附件
media_key = "tg_spider:media_id:{}"
# 发送人头像
sender_key = "tg_spider:sender_id:{}"
# kafka信息
msg_kafka_key = "tg_spider:kafkaMessage"
user_kafka_key = "tg_spider:kafkaUser"
group_kafka_key = "tg_spider:kafkaGroup"
sender_kafka_key = "tg_spider:kafkaSender"
# err信息
error_key = "tg_spider:errorMessage"
# 失效的链接
failure_key = "tg_spider:links_no_user"
# 全局redis
global_redis_pool = aioredis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, health_check_interval=30)
redis_client = aioredis.Redis(connection_pool=global_redis_pool)
media_redis_pool = aioredis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, db=1, health_check_interval=30)
media_avatar_redis_client = aioredis.Redis(connection_pool=media_redis_pool)

"""
oss配置
"""
oss_cfg = config["oss"]
oss_access_id = oss_cfg["access-id"]
oss_access_key = oss_cfg["access-key"]
oss_endpoint = oss_cfg["endpoint"]
oss_bucket = oss_cfg["bucket"]
auth = oss2.Auth(oss_access_id, oss_access_key)
bucket = oss2.Bucket(auth, oss_endpoint, oss_bucket)

oss_base_path = "crawler/tg{}"
save_url = "https://andata-tg-usa.oss-us-west-1.aliyuncs.com/{}"
