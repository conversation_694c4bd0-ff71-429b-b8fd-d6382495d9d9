# -*- coding: utf-8 -*-
import os
import re
import json
import time
import uuid
import typing
import asyncio
import hashlib
import asyncoss
from loguru import logger
from datetime import timezone
from dateutil.parser import parse
from telethon import TelegramClient, utils
from telethon.tl.types import ChannelParticipantsAdmins, InputMessagesFilterPinned
from telethon.tl.functions.channels import GetFullChannelRequest, GetParticipantsRequest
from utils.config import oss_base_path, save_url, media_key, sender_key, msg_kafka_key, error_key, auth, oss_endpoint, oss_bucket, redis_client, media_avatar_redis_client, sender_kafka_key, group_kafka_key


async def get_history_msg(client: TelegramClient, msg_num: int, channel: typing.Union[int, str], offset_id: int, reverse=False, entity=None, **kwargs) -> list:
    """
    获取频道历史信息
    :param client:             tg客户端实例
    :param msg_num:            检索消息条数超过3000条,请求时间超过30秒
    :param channel:            频道id或者群链接
    :param offset_id:          偏移消息（将检索此消息前或者后的消息）
    :param reverse:            消息排序方式,默认为False,获得最新的消息
    :param entity:             群组的实体
    :return:
    """
    result = []
    try:
        channel_id = entity.id
        channel_name = entity.title
        logger.info(f"正在采集 {channel_name} 的聊天消息")
        broadcast = entity.broadcast
        username = entity.username  # 电报链接用户名
        if broadcast:
            category = "broadcast"
        else:
            category = "group"
        if not username:
            category = "private_" + category
        # 采集群历史消息
        async for message in client.iter_messages(entity, wait_time=4, limit=msg_num, offset_id=offset_id, reverse=reverse, **kwargs):
            try:
                message_dict = await parse_message(client, message, channel_id, channel, channel_name)
                message_dict["category"] = category
                await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": message_dict}, ensure_ascii=False))
                result.append(message_dict)
                ext_links = message_dict.get("extLinks")
                if ext_links:
                    links = list(set([tg_link_extract(_) for _ in ext_links]))
                    for ext_link in links:
                        if ext_link:
                            await redis_client.sadd("tg_spider:identification:task", ext_link)
            except (Exception,) as e:
                logger.exception(f"解析频道聊天记录错误:{e},{message.to_dict()}")
                if "A wait of " in str(e):
                    wait_time = re.search("\d+", str(e))
                    if wait_time:
                        wait_time_int = int(wait_time.group())
                        await asyncio.sleep(wait_time_int)
    except Exception as e:
        await redis_client.sadd(error_key, f"tg群 {channel} 获取消息错误,错误为:{e} , offset_id:{offset_id}")
        logger.exception(f"获取频道聊天记录错误:{e}")
        # 提示太快了,需要休息
        if "A wait of " in str(e):
            wait_time = re.search("\d+", str(e))
            if wait_time:
                wait_time_int = int(wait_time.group())
                await asyncio.sleep(wait_time_int)
    finally:
        logger.info(f"===== 获取信息结束,获取到{len(result)}条消息========")
        return result


async def parse_message(client: TelegramClient, message, channel_id=None, channel=None, channel_name=None) -> dict:
    """
    解析消息
    :param client:         账号客户端
    :param message:        消息实体
    :param channel_id:     群id
    :param channel:        群链接
    :param channel_name:   群名字
    :return:
    """
    message_json = json.loads(message.to_json())
    media_json = message_json.get("media")
    message_text = message_json.get("message") or ""
    media_type = ""
    message_media_id = ""
    media_info = ""
    # 采集附件
    if media_json:
        message_media_id, media_info, media_type = await download_media(client, message, media_json, channel, channel_id)
    msg_date = message_json.get("date")
    beijing_timestamp = parse(msg_date).replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp()
    reply_to = message_json.get("reply_to") or {}
    msg_reply_id = reply_to.get("reply_to_msg_id", "")
    from_id = message_json.get("from_id") or {}
    user_id = from_id.get("user_id", "")
    # 发送人信息
    send_nick_name = str(utils.get_display_name(message.sender))
    # 消息归属者信息
    sender_id = ""
    sender_avatar = ""
    # 系统消息
    if message_json.get("action"):
        media_type = "system"
        action_type = message_json.get("action").get("_")
        if action_type == "MessageActionChatAddUser":
            if send_nick_name:
                media_info = f"{send_nick_name} 已加入群组"
            else:
                media_info = f"已注销账号 已加入群组"
        elif action_type == "MessageActionChatJoinedByLink":
            if send_nick_name:
                media_info = f"{send_nick_name} 通过邀请链接加入群组"
            else:
                media_info = f"已注销账号 通过邀请链接加入群组"
        elif action_type == "MessageActionChatDeleteUser":
            if send_nick_name:
                media_info = f"{send_nick_name} 离开群组"
            else:
                media_info = f"已注销账号 离开群组"
        elif action_type == "MessageActionChannelCreate":
            media_info = f"{send_nick_name}群组已经创建"
    # 消息归属者信息
    if message.sender:
        sender_dict = message.sender.to_dict()
        sender_type = sender_dict.get("_") or ""
        sender_id = sender_dict.get("id") or ""
        first_name = sender_dict.get("first_name")
        last_name = sender_dict.get("last_name")
        user_name = sender_dict.get("username")
        phone = sender_dict.get("phone")
        status = sender_dict.get("status") or {}
        online_time = status.get("was_online", status.get("expires"))
        if online_time:
            online_time = int(online_time.replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp() * 1000)
        unique_id = f"{channel_id}_{sender_id}"
        sender_unique_id_key = sender_key.format(unique_id)
        sender_avatar = await media_avatar_redis_client.get(sender_unique_id_key)
        if not sender_avatar and sender_type == "User":
            sender_avatar = ""
            sender_avatar_byte = await client.download_profile_photo(entity=message.sender, file=bytes)
            if sender_avatar_byte:
                oss_path = oss_base_path.format(f"/sender/{channel_id}/{sender_id}.jpg")
                upload_success = await upload_file_to_oss(oss_path, sender_avatar_byte)
                if not upload_success:
                    await redis_client.sadd(error_key, f"用户【{sender_id}】头像上传失败")
                sender_avatar = save_url.format(oss_path)
                await media_avatar_redis_client.set(sender_unique_id_key, sender_avatar, nx=True, ex=1 * 60 * 60 * 6)
        sender_dict = {"firstName": first_name, "lastName": last_name, "userName": user_name, "phone": phone, "avatar": sender_avatar, "onLineTime": online_time, "userId": sender_id, "channelId": channel_id}
        sender_message = {"Key": "sender", "Body": sender_dict}
        await redis_client.sadd(sender_kafka_key, json.dumps(sender_message, ensure_ascii=False))

    # 消息里面的一些链接
    entities = message_json.get("entities") or []
    ext_links = [url for url in list(set([_["url"] for _ in entities if _["_"] == "MessageEntityTextUrl"])) if url.startswith("https://t.me")]
    message_dict = {
        "channelId": int(channel_id),
        "link": str(channel),
        "channelName": str(channel_name),
        "content": message_text,
        "sendNickName": send_nick_name,
        "sendTime": int(beijing_timestamp),
        "sendUid": user_id,
        "msgId": message_json.get('id'),
        "msgReplyId": msg_reply_id,
        "mediaId": message_media_id,
        "mediaType": media_type,
        "media": media_info,
        "extLinks": ext_links,
        "senderId": sender_id,
        "senderAvatar": sender_avatar
    }
    return message_dict


async def get_group_info(client: TelegramClient, channel: str, entity):
    """
    获取群组或者频道详细信息的方法
    :param client:        客户端
    :param channel:       群链接
    :param entity:        获取到的群实体
    :return:
    """
    # 获取群组信息
    can_view_participants = False
    member_count = 0
    try:
        full_channel_info = await client(GetFullChannelRequest(entity))
        full_channel_info_json = full_channel_info.to_dict()
        full_chat = full_channel_info_json.get("full_chat")
        desc = full_chat.get("about")  # 描述
        can_view_participants = full_chat.get("can_view_participants")
        logger.info(f"开始采集群管理员,获取权限是 {can_view_participants}")
        member_count = full_chat.get("participants_count", 0)
    except (Exception,) as e:
        logger.error(e)
        desc = ""
    name = entity.title  # 电报名称
    username = None
    if hasattr(entity, "username"):
        username = entity.username  # 电报链接用户名
    broadcast = entity.broadcast  # 电报分类
    cover_img = ""
    private = False
    # 下载群组头像
    photo_bytes = await client.download_profile_photo(entity=entity, file=bytes)
    if photo_bytes:
        oss_path = oss_base_path.format(f"/channel/{entity.id}.jpg")
        upload_success = await upload_file_to_oss(oss_path, photo_bytes)
        if not upload_success:
            await redis_client.sadd(error_key, f"群组【{channel}】头像上传失败")
        cover_img = save_url.format(oss_path)
    # 判断群组属性是群组还是频道是否是私有
    if isinstance(broadcast, bool):
        if broadcast:
            category = "broadcast"
        else:
            category = "group"
    else:
        category = "unknown"
    if username:
        channel = f"https://t.me/{username}"
    else:
        private = True
        category = "private_" + category
    _id = entity.id
    # 获取置顶消息
    logger.info(f"开始采集置顶消息")
    ids = ""
    async for message in client.iter_messages(entity=entity, wait_time=4, filter=InputMessagesFilterPinned, limit=1000):
        try:
            pinned_message_dict = await parse_message(client, message, _id, channel, name)
            pinned_message_dict["category"] = category
            if private:
                pinned_message_dict["msgId"] = pinned_message_dict["sendTime"]
            await redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": pinned_message_dict}, ensure_ascii=False))
            ids += str(pinned_message_dict["msgId"]) + ","
        except (Exception,) as e:
            logger.error(e)
            # 提示太快了,需要休息
            if "A wait of " in str(e):
                wait_time = re.search("\d+", str(e))
                if wait_time:
                    wait_time_int = int(wait_time.group())
                    await asyncio.sleep(wait_time_int)
    ids = ids.strip(",")
    group_info = {
        "name": name,
        "desc": desc,
        "link": channel,
        "memberCount": member_count,
        "category": category,
        "origin": "app",
        "id": _id,
        "coverImg": cover_img,
        "topMsgIds": ids
    }
    await redis_client.sadd(group_kafka_key, json.dumps({"Key": "channel", "Body": group_info}, ensure_ascii=False))
    logger.info("群基础信息采集完成")

    if can_view_participants:
        participants = await client(GetParticipantsRequest(entity, filter=ChannelParticipantsAdmins(), offset=0, limit=100, hash=0))
        type_info_dict = {}
        for _ in participants.participants:
            type_dict = _.to_dict()
            user_id = type_dict["user_id"]
            date = type_dict.get("date", "")
            beijing_timestamp = ""
            if date:
                beijing_timestamp = int(date.replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp())
            type_info_dict[user_id] = {"userType": type_dict["_"], "date": beijing_timestamp}
        for administrator in participants.users:
            try:
                user_id = administrator.id
                type_info = type_info_dict.get(user_id, {})
                sender_dict = administrator.to_dict()
                sender_id = sender_dict.get("id") or ""
                first_name = sender_dict.get("first_name")
                last_name = sender_dict.get("last_name")
                user_name = sender_dict.get("username")
                phone = sender_dict.get("phone")
                status = sender_dict.get("status") or {}
                online_time = status.get("was_online", status.get("expires"))
                if online_time:
                    online_time = int(online_time.replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp() * 1000)
                unique_id = f"{_id}_{user_id}"
                sender_unique_id_key = sender_key.format(unique_id)
                sender_avatar = await media_avatar_redis_client.get(sender_unique_id_key)
                if not sender_avatar:
                    sender_avatar_byte = await client.download_profile_photo(entity=administrator, file=bytes)
                    if sender_avatar_byte:
                        oss_path = oss_base_path.format(f"/sender/{_id}/{user_id}.jpg")
                        upload_success = await upload_file_to_oss(oss_path, sender_avatar_byte)
                        if not upload_success:
                            await redis_client.sadd(error_key, f"群成员【{user_id}】头像上传失败")
                        sender_avatar = save_url.format(oss_path)
                        await media_avatar_redis_client.set(sender_unique_id_key, sender_avatar, nx=True, ex=1 * 60 * 60 * 24)
                sender_dict = {"firstName": first_name, "lastName": last_name, "userName": user_name, "phone": phone, "avatar": sender_avatar, "onLineTime": online_time, "userId": sender_id, "channelId": _id}
                sender_dict.update(type_info)
                sender_message = {"Key": "sender", "Body": sender_dict}
                await redis_client.sadd(sender_kafka_key, json.dumps(sender_message, ensure_ascii=False))
            except (Exception,) as e:
                logger.error(e)
    logger.info(f"群组详细信息采集完成")
    return group_info


async def upload_file_to_oss(oss_path, file_bytes, max_number=5, **kwargs):
    """
    上传文件到 oss
    :param oss_path:      oss存放路径
    :param file_bytes:    要上传文件的bytes
    :param max_number:    最大重试次数
    :return:
    """
    async with asyncoss.Bucket(auth, oss_endpoint, oss_bucket) as bucket:
        while max_number > 0:
            try:
                await bucket.put_object(oss_path, file_bytes, **kwargs)
                return True
            except (Exception,) as e:
                logger.error(e)
                max_number -= 1


zip_types = ["application/zip", "application/vnd.rar", "application/epub+zip", "application/x-7z-compressed", "application/x-compress", "application/x-xz", "application/gzip",
             "application/x-xz-compressed-tar", "application/x-bzip", "application/x-tar", "application/rar", "application/x-lzip", "application/x-rar", "application/x-compressed-tar",
             "application/x-bzip-compressed-tar", "application/x-gzip", "application/x-tarz", "application/vnd.comicbook-rar"]

office_types = ["application/vnd.openxmlformats-officedocument.presentationml.presentation", "application/vnd.ms-excel", "application/msword", "application/pdf", "text/csv",
                "application/wps-office.xlsx", "application/wps-office.xls", "application/x-mobipocket-ebook", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text"]

file_downloads_types = []
file_downloads_types.extend(zip_types)
file_downloads_types.extend(office_types)


async def download_media(client: TelegramClient, message, media_json, channel, channel_id):
    media_type = media_json.get("_")
    local_time = time.localtime(time.time())
    now_time = f"{local_time.tm_year}-{local_time.tm_mon}"
    oss_path = None
    media_bytes = None
    message_media_id = ""
    message_unique_id_key = None
    media_headers = {'x-oss-expires': '94608000'}
    # V1.2版本需求, 还需要完善
    storage = await redis_client.sismember("tg_spider:storage_group", channel_id)
    # 图片下载
    if "MessageMediaPhoto" == media_type:
        media_type = "photo"
        message_media_id = media_json.get("photo").get("id", "")
        message_unique_id_key = media_key.format(message_media_id)
        media_info = await media_avatar_redis_client.get(message_unique_id_key)
        if not media_info:
            oss_path = oss_base_path.format(f"/{media_type}/{now_time}/{message_media_id}.jpg")
            media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type, thumb=True)
            if media_bytes:
                media_info = save_url.format(oss_path)
    # 下载当前消息中的媒体文件
    elif "MessageMediaDocument" == media_type:
        media_type = media_json.get("document").get("mime_type")
        # 视频
        if media_type.startswith("video"):
            media_type = "video"
        # 图片
        elif media_type.startswith("image"):
            media_type = "photo"
        # 表情包
        elif media_type == "application/x-tgsticker":
            media_type = "emoji"
        # 音频
        elif media_type.startswith("audio"):
            media_type = "audio"
        # 文本
        elif media_type == "text/plain":
            media_type = "text"
        message_media_id = media_json.get("document").get("id", "")
        message_unique_id_key = media_key.format(message_media_id)
        media_info = await media_avatar_redis_client.get(message_unique_id_key)
        if not media_info:
            # 下载表情包 和 图片
            if media_type in ["emoji", "photo"]:
                oss_path = oss_base_path.format(f"/{media_type}/{now_time}/{message_media_id}.jpg")
                media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type, thumb=True)
            # 下载音频
            elif media_type == "audio":
                oss_path = oss_base_path.format(f"/{media_type}/{now_time}/{message_media_id}.mp3")
                media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
            # 下载apk
            elif media_type == "application/vnd.android.package-archive":
                file_name = message.file.name
                if file_name is None:
                    file_name = uuid.uuid4().hex + ".apk"
                file_size = message.file.size
                file_extension = os.path.splitext(file_name)[-1]
                oss_path = oss_base_path.format(f"/apk/{now_time}/{calculate_md5(str(file_size) + file_name)}{file_extension}")
                media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
                if media_bytes:
                    media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "ossPath": save_url.format(oss_path)}, ensure_ascii=False)
            # 下载视频
            elif media_type == "video":
                file_name = message.file.name
                file_size = message.file.size
                if file_name is None:
                    file_name = uuid.uuid4().hex + ".mp4"
                if storage:
                    # 大于100mb的文件不下载
                    if file_size > 100 * 1024 * 1024:
                        media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "desc": "附件大于100MB"}, ensure_ascii=False)
                    else:
                        file_extension = os.path.splitext(file_name)[-1]
                        oss_path = oss_base_path.format(f"/{media_type}/{now_time}/{calculate_md5(str(file_size) + file_name)}{file_extension}")
                        media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
                        if media_bytes:
                            media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "ossPath": save_url.format(oss_path)}, ensure_ascii=False)
                else:
                    oss_path = oss_base_path.format(f"/video/{now_time}/{message_media_id}.jpg")
                    if not message.media.document.thumbs or len(message.media.document.thumbs) == 0:
                        media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "desc": "没有缩略图"}, ensure_ascii=False)
                    else:
                        media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type, thumb=True)
            # 下载office、zip、text
            elif media_type in file_downloads_types and storage:
                file_name = message.file.name
                file_size = message.file.size
                # 大于100mb的文件不下载
                if file_size > 100 * 1024 * 1024:
                    media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "desc": "附件大于100MB"}, ensure_ascii=False)
                else:
                    if media_type in zip_types:
                        oss_dri = "zip"
                    elif media_type in office_types:
                        oss_dri = "office"
                    else:
                        oss_dri = media_type
                    file_extension = os.path.splitext(file_name)[-1]
                    oss_path = oss_base_path.format(f"/{oss_dri}/{now_time}/{calculate_md5(str(file_size) + file_name)}{file_extension}")
                    media_bytes = await download_media_bytes(client=client, message=message, channel=channel, oss_path=oss_path, media_type=media_type)
                    if media_bytes:
                        media_info = json.dumps({"fileName": file_name, "fileSize": file_size, "ossPath": save_url.format(oss_path)}, ensure_ascii=False)
            if oss_path and not media_info:
                media_info = save_url.format(oss_path)
    # 采集超链接
    elif "MessageMediaWebPage" == media_type:
        media_type = "link"
        url = media_json.get("webpage").get("url")
        description = media_json.get("webpage").get("description")
        media_info = json.dumps({"url": url, "desc": description}, ensure_ascii=False)
    else:
        media_info = json.dumps(media_json, ensure_ascii=False)
    if media_bytes and oss_path:
        upload_success = await upload_file_to_oss(oss_path, media_bytes, headers=media_headers)
        if not upload_success:
            await redis_client.sadd("tg_spider:upload_fail", json.dumps({"channel": channel, "offset_id": message.id, "oss_path": oss_path, "media_type": media_type}))
        else:
            await media_avatar_redis_client.set(message_unique_id_key, media_info, nx=True, ex=1 * 60 * 60 * 24)
    return message_media_id, media_info, media_type


async def download_media_bytes(client, message, channel, oss_path, media_type, thumb=False):
    media_bytes = None
    max_download_number = 3
    while max_download_number > 0:
        try:
            logger.info(f"Downloading media bytes from 【{oss_path}】, channel is 【{channel}】")
            max_download_number -= 1
            if thumb:
                media_bytes = await client.download_media(message=message, file=bytes, thumb=-1, progress_callback=callback)
            else:
                media_bytes = await client.download_media(message=message, file=bytes, progress_callback=callback)
            if media_bytes:
                return media_bytes
            else:
                logger.warning(message.to_json())
        except Exception as e:
            logger.warning(e)
            await asyncio.sleep(3)
    if not media_bytes:
        await redis_client.sadd("tg_spider:upload_fail", json.dumps({"channel": channel, "offset_id": message.id, "oss_path": oss_path, "media_type": media_type}))


def calculate_md5(data):
    md5_hash = hashlib.md5()
    md5_hash.update(data.encode('utf-8'))
    return md5_hash.hexdigest()


def callback(current, total):
    print('Downloaded', current, 'out of', total, 'bytes: {:.2%}'.format(current / total))


def logger_callback(current, total):
    logger.info('Downloaded {} out of {} bytes: {:.2%}'.format(current, total, current / total))


def get_machine_ip():
    """
    获取服务器ip
    :return:
    """
    import socket
    socket_server = None
    try:
        socket_server = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        socket_server.connect(('*******', 80))
        ip = socket_server.getsockname()[0]
        return ip
    finally:
        if socket_server:
            socket_server.close()


def tg_link_extract(link):
    rule_one = re.search("https://t\.me/[^/?#]{1,100}", link)
    if rule_one:
        return rule_one.group().replace("@", "").rstrip("https:")


def is_russian(text):
    return bool(re.search('[\u0400-\u04FF]', text))


if __name__ == '__main__':
    s = "Взаимные подписки | Пиар анкет | ВП"
    print(is_russian(s))
