# -*- coding: utf-8 -*-
import json
import asyncio

import aioredis
from loguru import logger
from telethon import events, TelegramClient
from telethon.sessions import StringSession
from telethon.tl.functions.messages import GetPeerDialogsRequest, GetUnreadMentionsRequest
from utils.crawl_tools import parse_message, get_machine_ip, tg_link_extract
from utils.config import msg_kafka_key, api_id, api_hash, error_key, log_path, redis_host, redis_port, redis_password


class GroupMonitor(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # redis客户端
        redis_pool = aioredis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, health_check_interval=30)
        self.redis_client = aioredis.Redis(connection_pool=redis_pool)
        # 账号的key
        self.account_key = "tg_spider:monitor_account:{}"
        # 群组基础信息的key
        self.group_info_key = "tg_spider:group_info"
        # 数据库群组最大的消息偏移id
        self.offset_key = "tg_spider:crawl:max_offset"
        # 封禁账号的key
        self.banned_key = "tg_spider:monitor_banned"

    async def login(self, account_info, machine_ip):
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"群监控登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(self.banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            error_mess = f"群监控登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            logger.error(error_mess)
            await self.redis_client.sadd(self.banned_key, json.dumps({"binding_account_ip": machine_ip, "banned_phone": phone}, ensure_ascii=False))
            await self.redis_client.sadd(error_key, error_mess)

    async def monitor(self, account_info, machine_ip):
        """
        获取群的实时消息
        :return:
        """
        while True:
            try:
                phone = account_info.get("phone")
                client = await self.login(account_info, machine_ip)
                logger.info(f"{phone} 开始监听新消息")
                # 获取账号所有的未读消息,防止监听群漏消息了
                await self.get_unread_messages(client)

                @client.on(events.NewMessage())
                @client.on(events.ChatAction())
                async def my_event_handler(event):
                    try:
                        # 过滤掉所有的私聊消息
                        if event.is_private is False:
                            private = False
                            chat = await event.get_chat()
                            channel_id = chat.id
                            chat_dict = chat.to_dict()
                            channel_name = chat_dict.get("title")
                            if chat_dict.get("broadcast"):
                                category = "broadcast"
                            else:
                                category = "group"
                            # 有username的就是公开,没有的就是私有
                            username = chat_dict.get("username")
                            if not username:
                                category = "private_" + category
                                channel = ""
                                private = True
                            else:
                                channel = f"https://t.me/{username}"
                            # 解析消息
                            if hasattr(event, 'message'):
                                message = event.message
                            else:
                                message = event.action_message
                            # 解析消息
                            message_dict = await parse_message(client, message, channel_id, channel, channel_name)
                            if message_dict["channelId"] is None:
                                error_mess = f"实时监控 channelId is NULL,channel【{channel}】,channel_name【{channel_name}】"
                                await self.redis_client.sadd(error_key, error_mess)
                            # 标记消息为已读
                            message_dict["category"] = category
                            if private:
                                message_dict["msgId"] = message_dict["sendTime"]
                            await client.send_read_acknowledge(event.chat, message)
                            # 存入redis,统一消费到kafka
                            await self.redis_client.sadd(msg_kafka_key, json.dumps({"Key": "msg", "Body": message_dict}, ensure_ascii=False))
                            max_offset = message_dict.get("msgId", "0")
                            logger.info(f"【{phone}】获取到【{channel_name}】,id:【{max_offset}】的消息")
                            # 记录好当前群组的偏移值
                            await self.redis_client.hset(self.offset_key, channel_id, max_offset)
                            # 拓展消息中的群链接
                            ext_links = message_dict.get("extLinks")
                            if ext_links:
                                links = list(set([tg_link_extract(link) for link in ext_links]))
                                await self.redis_client.sadd("tg_spider:identification:task", *links)
                    except (Exception,) as e:
                        logger.exception(e)

                await client.run_until_disconnected()
            except (Exception,) as e:
                logger.error(e)

    async def get_unread_messages(self, client):
        try:
            dialogs = await client.get_dialogs()
            channel_dialogs = [dialog for dialog in dialogs if dialog.is_channel]
            for dialogs in channel_dialogs:
                unread_count = dialogs.unread_count
                channel_username = dialogs.entity.username
                channel_id = dialogs.entity.id
                if int(unread_count) < 1:
                    continue
                if not channel_username:
                    link = await self.redis_client.hget(self.group_info_key, channel_id)
                else:
                    link = "https://t.me/" + channel_username
                if not link:
                    continue
                peer = await client(GetPeerDialogsRequest([dialogs]))
                min_id = peer.dialogs[0].read_inbox_max_id
                max_id = peer.dialogs[0].read_outbox_max_id
                await self.redis_client.sadd("tg_spider:crawl:missing_data_info", json.dumps({"link": link, "min_id": str(min_id), "max_id": str(max_id)}))
                await client.send_read_acknowledge(dialogs)
        except (Exception,) as e:
            logger.error(e)

    async def start(self):
        while True:
            try:
                machine_ip = get_machine_ip()
                account_key = self.account_key.format(machine_ip)
                account_list = await self.redis_client.get(account_key) or "[]"
                account_infos = json.loads(account_list)
                tasks = [self.monitor(account_info, machine_ip) for account_info in account_infos]
                await asyncio.gather(*tasks)
            except (Exception,) as e:
                logger.exception(e)
            finally:
                await asyncio.sleep(60 * 10)


if __name__ == '__main__':
    group_monitor = GroupMonitor()
    asyncio.run(group_monitor.start())
