### 项目说明

```
电报群组消息抓取,手机号检索,推广等.有supervisor模块进行脚本保活
```

### 服务器脚本目录

```
/andata/server/crawler/tg_message_crawler

```

### 主要功能以及代码位置

| 文件名                          | 功能描述                                 |
|------------------------------|--------------------------------------|
| group_msg_crawl.py           | 群组消息抓取模块,有历史消息抓取,更新消息抓取,遗漏数据抓取 3 个功能 |
| group_monitor.py             | 群组消息监控模块                             |
| group_monitor_tools.py       | 群监控管理,负责加退群等                         |
| phone_search_interface.py    | 手机号实时搜索接口                            |
| promotion.py                 | 广告推广                                 |
| search_user_info_by_phone.py | 手机号批量搜索                              |
| link_identification.py       | 拓展链接分类                               |
| strat.sh                     | 项目启动的 shell 脚本                       |
| utils/redis_utils.py         | 一些统计用的小工具                            |
| submit_data.py               | 提交数据的程序                              |

### 项目启动命令

```shell
cd /andata/server/crawler/tg_message_crawler
. venv/bin/activate
# 启动手机号搜索接口
nohup gunicorn -c kepler_interface_config.py kepler_interface:app >/dev/null 2>&1 &
# 重启服务
pstree -ap|grep gunicorn
|-grep,6194 --col gunicorn
  |   `-gunicorn,30080 /usr/local/bin/gunicorn collect:app -c collect_gunicorn.py
  |       |-gunicorn,4413 
kill -HUP 30080
# 启动数据提交
nohup python submit_data.py >/dev/null 2>&1 &
# 其他启动在shell脚本中,阿里云控制台有批量启动命令
```

### 用户信息 sender 类型采集字段说明

| 字段名        | 字段描述             | 是否可以为空  |
|------------|------------------|---------|
| firstName  | 用户昵称的姓           | 是       |
| lastName   | 用户昵称的名           | 是       |
| userName   | 用户名              | 是       |
| phone      | 用户手机号            | 是       |
| avatar     | 用户头像             | 是       |
| onLineTime | 用户最后在线时间         | 是       |
| userId     | 用户 ID            | 否 可能为 0 |
| channelId  | 采集到的这个用户信息的群组 id | 否       |
| userType   | 群主和管理员标识         | 是       |
| date       | 设置成群主或者管理员的日期    | 是       |

### 用户信息 user 类型采集字段说明

| 字段名        | 字段描述     | 是否可以为空 |
|------------|----------|--------|
| firstName  | 用户昵称的姓   | 是      |
| lastName   | 用户昵称的名   | 是      |
| userName   | 用户名      | 是      |
| userPhone  | 用户手机号    | 否      |
| avatar     | 用户头像     | 是      |
| onLineTime | 用户最后在线时间 | 是      |
| userId     | 用户 ID    | 否      |
| about      | 用户个性签名   | 是      |

### 群基础信息channel类型采集字段说明

| 字段名         | 字段描述                                       | 是否可以为空 |
|-------------|--------------------------------------------|--------|
| name        | 群组名字                                       | 否      |
| desc        | 群组公告                                       | 是      |
| link        | 群组链接                                       | 是      |
| memberCount | 群组人数                                       | 否      |
| category    | 群组类型,broadcast是频道,group是群组,私有的前面加上private_ | 否      |
| origin      | 采集来源                                       | 否      |
| id          | 群组 ID                                      | 否      |
| coverImg    | 群组头像                                       | 是      |
| topMsgIds   | 群组置顶消息id                                   | 是      |

### 群消息msg类型采集字段说明

| 字段名          | 字段描述                 | 是否可以为空 |
|--------------|----------------------|--------|
| channelId    | 消息所属群组id             | 否      |
| link         | 消息所属群组链接             | 是      |
| channelName  | 消息所属群组名字             | 否      |
| content      | 消息内容                 | 是      |
| sendNickName | 消息产生人昵称              | 是      |
| sendTime     | 消息发送时间               | 否      |
| sendUid      | 消息发送人id              | 否      |
| msgId        | 消息id                 | 否      |
| msgReplyId   | 如果是回复的,这个是回复的那条消息的id | 是      |
| mediaId      | 消息附件id               | 是      |
| mediaType    | 消息附件类型               | 是      |
| media        | 消息附件地址               | 是      |
| extLinks     | 消息中的外部链接             | 是      |
| senderId     | 消息产生人id              | 是      |
| senderAvatar | 消息产生人头像              | 是      |
