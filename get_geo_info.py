#!/usr/bin/python3
# -*- coding: utf-8 -*-

"""
@project: tg_message_crawler
@file: get_geo_info.py
@function:
@time: 2024/6/25 14:12
"""
import json
import httpx
import random
import asyncio
from loguru import logger
from datetime import timezone
from telethon import TelegramClient, types
from telethon.tl.functions import contacts
from telethon.sessions import StringSession
from utils.crawl_tools import upload_file_to_oss, get_history_msg
from utils.developer_infos import developer_list
from telethon.tl.functions.channels import GetFullChannelRequest
from utils.config import log_path, error_key, oss_base_path, save_url, redis_client, media_avatar_redis_client, sender_kafka_key, group_kafka_key


class GetGeoInfo(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # 任务的key
        self.task_key = "tg_spider:geo_task"
        # 结果的key
        self.results_key = "tg_spider:geo_results:"
        # 账号的key
        self.account_key = "tg_spider:geo_accounts"
        # 代理url
        self.port_url = "http://api.ipweb.cc:8004/api/agent/account2?country=HK&times=90&limit=1"

    async def login(self, account_str, proxy):
        account_info = json.loads(account_str)
        phone = account_info.get("phone")
        try:
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {proxy}")
            string_session = account_info.get("account_key")
            developer_info = random.choice(developer_list)
            api_id = developer_info["api_id"]
            api_hash = developer_info["api_hash"]
            client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy, timeout=10)
            try:
                await client.connect()
            except OSError as e:
                logger.error(e)
                return
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"坐标搜索账号【{phone}】登录失败，登录的api信息是 【{developer_info}】"
                logger.error(f"【{phone}】被封号了")
                await redis_client.srem(self.account_key, account_str)
                await redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            logger.warning(account_str)
            if "two different IP" in str(e):
                await redis_client.srem(self.account_key, account_str)
                error_mess = f"坐标搜索账号【{phone}】登录失败"
                await redis_client.sadd(error_key, error_mess)
            return

    async def get_geo_info(self):
        while True:
            task = None
            task_id = None
            try:
                task_count = await redis_client.scard(self.task_key)
                if task_count == 0:
                    await asyncio.sleep(10)
                    continue
                logger.info(f"坐标搜索任务剩余【{task_count}】")
                account_balance = await redis_client.scard(self.account_key) or 0
                if account_balance < 80:
                    error_mess = f"坐标搜索账号剩余数量低,剩余【{account_balance}】,请尽快补充账号"
                    supplement = 80 - account_balance
                    supplement_account = await redis_client.spop("tg_spider:temp_banned", supplement)
                    if supplement_account:
                        await redis_client.sadd(self.account_key, *supplement_account)
                    await redis_client.sadd(error_key, error_mess)
                account_str = await redis_client.srandmember(self.account_key)
                account_info = json.loads(account_str)
                crawler_phone = account_info.get("phone")
                requests_num_key = "tg_spider:geo_search_requests_num:{}".format(crawler_phone)
                cd_key = "tg_spider:geo_search_cd:{}".format(crawler_phone)
                # 获取是否在抓取cd里面
                cd_status = await redis_client.get(cd_key)
                # 获取请求次数
                number_of_requests = await redis_client.get(requests_num_key) or 0
                if int(number_of_requests) < 50 and not cd_status and task_count > 0:
                    proxy_key = f"tg_spider:account_temp_proxy:{crawler_phone}"
                    proxy = await self.get_proxy(proxy_key)
                    client = await self.login(account_str, proxy)
                    assert client
                    task = await redis_client.spop(self.task_key)
                    if not task:
                        continue
                    logger.info(f"开始坐标搜索任务【{task}】")
                    task_obj = json.loads(task)
                    lat = float(task_obj.get("lat"))
                    lng = float(task_obj.get("lng"))
                    task_id = task_obj.get("taskId")
                    result = await client(contacts.GetLocatedRequest(geo_point=types.InputGeoPoint(lat=lat, long=lng)))
                    print(result.to_json())
                    logger.info(f"附近用户数量【{len(result.users)}】")
                    logger.info(f"附近群组数量【{len(result.chats)}】")
                    users = []
                    groups = []
                    distance_dict = {_.get("peer").get("user_id", _.get("peer").get("channel_id")): _.get("distance") for _ in result.updates[0].to_dict().get("peers")}
                    # 解析用户信息
                    for user in result.users:
                        try:
                            first_name = user.first_name
                            last_name = user.last_name
                            user_name = user.username
                            phone = user.phone
                            status = user.status or {}
                            online_time = None
                            if hasattr(status, "was_online"):
                                online_time = status.was_online
                            elif hasattr(status, "expires"):
                                online_time = status.expires
                            if online_time:
                                online_time = int(online_time.replace(tzinfo=timezone.utc).astimezone(tz=None).timestamp() * 1000)
                            user_id = user.id
                            avatar_unique_id_key = "tg_spider:sender_id:{}".format(user_id)
                            avatar = await media_avatar_redis_client.get(avatar_unique_id_key)
                            if not avatar:
                                avatar = ""
                                # avatar_byte = await client.download_profile_photo(entity=user, file=bytes)
                                # if avatar_byte:
                                #     oss_path = oss_base_path.format(f"/sender/{user_id}.jpg")
                                #     upload_success = await upload_file_to_oss(oss_path, avatar_byte)
                                #     if not upload_success:
                                #         await redis_client.sadd(error_key, f"用户【{user_id}】头像上传失败")
                                #     avatar = save_url.format(oss_path)
                                #     await media_avatar_redis_client.set(avatar_unique_id_key, avatar, nx=True, ex=1 * 60 * 60 * 6)
                            distance = distance_dict.get(user_id, 0)
                            user_info = {
                                "firstName": first_name,
                                "lastName": last_name,
                                "userName": user_name,
                                "phone": phone,
                                "avatar": avatar,
                                "onLineTime": online_time,
                                "userId": user_id
                            }
                            sender_message = {"Key": "user", "Body": user_info}
                            user_info["distance"] = distance
                            await redis_client.sadd(sender_kafka_key, json.dumps(sender_message, ensure_ascii=False))
                            users.append(user_info)
                        except Exception as e:
                            logger.error(f"解析用户信息错误: {e}")
                    # 解析群组信息
                    for chat in result.chats:
                        try:
                            try:
                                full_channel_info = await client(GetFullChannelRequest(chat))
                                full_channel_info_json = json.loads(full_channel_info.to_json())
                                full_chat = full_channel_info_json.get("full_chat")
                                desc = full_chat.get("about")
                            except Exception as e:
                                desc = ""
                            _id = chat.id
                            distance = distance_dict.get(_id)
                            name = chat.title
                            member_count = chat.participants_count
                            broadcast = chat.broadcast  # 电报分类
                            username = chat.username  # 电报链接用户名
                            if isinstance(broadcast, bool):
                                if broadcast:
                                    category = "broadcast"
                                else:
                                    category = "group"
                            else:
                                category = "unknown"
                            if not username:
                                category = "private_" + category
                                channel = ""
                            else:
                                channel = f"https://t.me/{username}"
                                await redis_client.sadd("tg_spider:crawl:history:task_links", channel)
                            photo_bytes = await client.download_profile_photo(entity=chat, file=bytes)
                            cover_img = ""
                            if photo_bytes:
                                oss_path = oss_base_path.format(f"/channel/{chat.id}.jpg")
                                upload_success = await upload_file_to_oss(oss_path, photo_bytes)
                                if not upload_success:
                                    await redis_client.sadd(error_key, f"群组【{chat.title}】头像上传失败")
                                cover_img = save_url.format(oss_path)
                            group_info = {
                                "name": name,
                                "desc": desc,
                                "link": channel,
                                "memberCount": member_count,
                                "category": category,
                                "id": _id,
                                "coverImg": cover_img,
                            }
                            channel_message = {"Key": "channel", "Body": group_info}
                            await redis_client.sadd(group_kafka_key, json.dumps(channel_message, ensure_ascii=False))
                            group_info["distance"] = distance
                            groups.append(group_info)
                        except Exception as e:
                            logger.error(e)
                    results = {"users": users, "groups": groups}
                    await redis_client.set(f"{self.results_key}{task_id}", json.dumps(results, ensure_ascii=False), ex=3 * 24 * 60 * 60)
                    logger.success("附近的人和群列表已经采集完成,开始采集附近的群消息")
                    # 采集群消息
                    for chat in result.chats:
                        try:
                            username = chat.username  # 电报链接用户名
                            if not username:
                                link = ""
                            else:
                                link = f"https://t.me/{username}"
                            await get_history_msg(client, 100, link, 0, False, chat)
                        except Exception as e:
                            logger.error(e)
                    logger.success(f"【{task}】采集完成")
                    await client.disconnect()
            except Exception as e:
                logger.error(e)
                if "Invalid geoposition provided (caused by GetLocatedRequest)" in str(e):
                    await redis_client.set(f"{self.results_key}{task_id}", "Invalid geoposition provided", ex=3 * 24 * 60 * 60)
                elif task:
                    await redis_client.sadd(self.task_key, task)

    async def get_proxy(self, proxy_key, max_number=10):
        proxy_str = await redis_client.get(proxy_key)
        if proxy_str:
            proxy = json.loads(proxy_str)
            return proxy
        while max_number > 0:
            try:
                await asyncio.sleep(0.5)
                resp = httpx.get(self.port_url, headers={"Token": "GJ7VJ0URV2AOSAUET5WSN8O7MB4GCKG0"})
                username = resp.json()["data"][0].split(":")[0]
                password = resp.json()["data"][0].split(":")[1]
                proxy = {
                    'proxy_type': 'http',
                    'addr': "gate1.ipweb.cc",
                    'port': 7778,
                    'username': username,
                    'password': password,
                    'rdns': True
                }
                await redis_client.set(proxy_key, json.dumps(proxy, ensure_ascii=False), ex=88 * 60)
                return proxy
            except (Exception,) as e:
                max_number -= 1
                logger.error(e)
                await asyncio.sleep(1)

    async def start(self):
        while True:
            try:
                task_number = await redis_client.scard(self.task_key)
                if task_number < 1:
                    continue
                logger.info("剩余任务数{}".format(task_number))
                tasks = []
                tasks.extend([self.get_geo_info() for _ in range(1)])
                await asyncio.gather(*tasks)
            except (Exception,) as e:
                logger.error(e)
            finally:
                await asyncio.sleep(10)


if __name__ == '__main__':
    get_geo_info = GetGeoInfo()
    asyncio.run(get_geo_info.start())
