#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2024/7/30 9:50
# @Site    : 
# @File    : join_group_crawler.py
# @Software: PyCharm
import random
import re
import json
import asyncio
import aioredis
from loguru import logger
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.tl.functions.messages import ImportChatInviteRequest
from utils.crawl_tools import get_machine_ip, get_group_info, get_history_msg
from utils.config import log_path, redis_host, redis_port, redis_password, error_key, api_id, api_hash


class JoinGroupCrawler(object):
    def __init__(self):
        logger.add(
            sink="%s_{time:YYYY_MM_DD}.log" % (log_path / f"{self.__class__.__name__}"),
            encoding="utf8",
            format="{time:YYYY-MM-DD HH:mm:ss} | {file} | line:{line} | func:{function} | {level} \n>>>\t{message}\n",
            rotation="00:00",
            retention="1 days"
        )
        # redis客户端
        redis_pool = aioredis.ConnectionPool.from_url("redis://{}".format(redis_host), port=redis_port, password=redis_password, encoding='utf-8', decode_responses=True, health_check_interval=30)
        self.redis_client = aioredis.Redis(connection_pool=redis_pool)
        # 账号的key
        self.account_key = "tg_spider:account:{}"
        # 任务的key
        self.task_key = "tg_spider:crawl:history:join_task_links"

    async def login(self):
        machine_ip = get_machine_ip()
        account_key = self.account_key.format(machine_ip)
        account_list = await self.redis_client.get(account_key) or "[]"
        accounts = json.loads(account_list)
        account_info = random.choice(accounts)
        phone = account_info.get("phone")
        try:
            string_session = account_info.get("account_key")
            client = TelegramClient(StringSession(string_session), api_id, api_hash, timeout=10)
            try:
                await client.connect()
            except OSError:
                error_mess = f"【{machine_ip}】登录失败,账号【{phone}】"
                logger.error('Failed to connect')
                await self.redis_client.sadd(error_key, error_mess)
            logger.info(f"账号【 {phone}】 开始登录,登录地址 {machine_ip}")
            is_user = await client.is_user_authorized()
            if not is_user:
                error_mess = f"加群采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
                logger.error(error_mess)
                await self.redis_client.sadd(error_key, error_mess)
            else:
                logger.success(f"【{phone}】登录成功")
                return client
        except (Exception,) as e:
            logger.error(e)
            error_mess = f"加群采集登录失败,服务器地址【{machine_ip}】,账号【{phone}】"
            logger.error(error_mess)
            await self.redis_client.sadd(error_key, error_mess)

    async def crawl_all_msg(self):
        while True:
            try:
                link = await self.redis_client.spop(self.task_key)
                if link is None:
                    break
                client = await self.login()
                if "joinchat" in link or "+" in link:
                    if "joinchat" in link:
                        hash_str = link.replace("https://t.me/joinchat/", "")
                    else:
                        hash_str = re.search("(?<=\+).*", link).group()
                    try:
                        await client(ImportChatInviteRequest(hash_str))
                    except(Exception,) as e:
                        print(e)
                entity = await client.get_entity(link)
                channel_name = entity.title
                logger.info(f"开始采集历史信息:【{channel_name}】,link: 【{link}】")
                # # 采集群组基础信息
                offset = 0
                msg_total = 0
                await get_group_info(client, link, entity)
                while True:
                    try:
                        msgs = await get_history_msg(client, 1000, link, int(offset), False, entity)
                        msg_len = len(msgs)
                        if msg_len > 0:
                            offset = msgs[-1].get("msgId", 0)
                            msg_total += msg_len
                        elif msg_len < 990:
                            break
                    except (Exception,) as e:
                        logger.exception(e)
                logger.info(f"【{link}】历史信息采集完毕 共采集了【{msg_total}】条消息")
                await client.disconnect()
            except (Exception,) as e:
                logger.exception(e)


if __name__ == '__main__':
    join_group_crawler = JoinGroupCrawler()
    asyncio.run(join_group_crawler.crawl_all_msg())
